export default {
	sysrole: {
		index: '#',
		beforeTheReturnTime: 'beforeTheReturnTime',
		permissionTip: 'grant',
		deleteDisabledTip: 'not allowed to delete',
		mustCheckOneTip: 'the assign permissions menu must be selected',
		roleCode: 'roleCode',
		roleDesc: 'role description',
		data_authority: 'data authority',
		createTime: 'createTime',
		please_enter_a_role_name: 'please enter a role name',
		please_enter_the_role_Code: 'please enter the role Code',
		please_enter_the_role_description: 'please enter the role description',
		menu_authority: 'menu authority',
		please_select: 'please select',
		importRoleTip: 'import role',
		groupName: 'enableStatus',
		templateType: 'notificationMethod',
		selectType: 'please choose anotification method',
		warehouse: 'warehouse',
		warehouseReminder: 'please Select Warehouse',
		roleName: 'name',
		inputRoleNameTip: 'please Enter The Name Of The Material',
		serialNumber: 'serialNumber',
		materialCode: 'materialCode',
		materialIdentification: 'materialIdentification',
		inventoryWarningValue: 'inventoryWarningValue',
		importFile: 'import',
		input_enter: 'please Enter ',
	},
};
