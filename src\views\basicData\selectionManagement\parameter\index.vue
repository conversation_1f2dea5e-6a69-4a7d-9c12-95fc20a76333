<template>
	<div>
		<el-row :gutter="24" class="w-[60%] h-[600px] mt30">
			<div class="w-full flex justify-around">
				<div>
					<h1 class="text-[30px] ml-10">电子标签参数</h1>
					<div class="mt-20">
						<el-form label-position="left" label-width="70px">
							<el-form-item label="蜂鸣器" prop="configName">
								<el-select v-model="form.elecLabelConfig.buzzer" placeholder="请选择蜂鸣器" style="width: 180px">
									<el-option v-for="item in buzzer" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item label="亮灯模式" prop="configName">
								<el-select v-model="form.elecLabelConfig.ledMode" placeholder="请选择亮灯模式" style="width: 180px">
									<el-option v-for="item in ledMode" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>
						</el-form>
					</div>
				</div>
				<div>


					<h1 class="text-[30px] ml-10">巷道灯参数</h1>
					<div class="mt-20">
						<el-form label-position="left" label-width="70px">
							<el-form-item label="蜂鸣器" prop="configName">
								<el-select v-model="form.shelfIndicatorConfig.buzzer" placeholder="请选择蜂鸣器" style="width: 180px">
									<el-option v-for="item in buzzer" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item label="亮灯模式" prop="configName">
								<el-select v-model="form.shelfIndicatorConfig.ledMode" placeholder="请选择亮灯模式" style="width: 180px">
									<el-option v-for="item in ledMode" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</div>
			<div class="mx-auto">
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</div>
		</el-row>
	</div>
</template>

<script setup lang="ts">
import { getObj, putObj } from '/@/api/basicData/selectionManagement/parameter';
import { useMessage} from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
// 提交表单数据
const form = reactive({
	elecLabelConfig: {
		buzzer: '',
		ledMode: '',
	},
	shelfIndicatorConfig: {
		buzzer: '',
		ledMode: '',
	},
});

const loading = ref(false);

// 提交
const onSubmit = async () => {
	loading.value = true;
	try {
		await putObj(form);
		useMessage().success(t('common.editSuccessText'));
	} catch (error:any) {
		useMessage().success(error.msg);

	} finally {
		loading.value = false;
		getData();
	}
};
const buzzer = [
	{
		label: '关闭',
		value: 0,
	},
	{
		label: '打开',
		value: 1,
	},
];
const ledMode = [
	{
		label: '常亮',
		value: 0,
	},
	{
		label: '快闪',
		value: 1,
	},
	{
		label: '慢闪',
		value: 2,
	},
	
];
const getData = async () => {
	const res = await getObj();
	form.elecLabelConfig = res.data.elecLabelConfig;
	form.shelfIndicatorConfig = res.data.shelfIndicatorConfig;
};
onMounted(() => {
	getData();
});
</script>
