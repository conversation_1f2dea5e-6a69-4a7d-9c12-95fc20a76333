<template>
	<div>
		<el-row>
			<div class="mb8 w-full">
				<div class="float-left">
					<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
						<el-form-item label="所属仓库" prop="warehouseId">
							<el-select
								placeholder="请选择所属仓库"
								v-model="state.queryForm.warehouseId"
								style="max-width: 180px"
								clearable
								@change="getZoneCodeSelect"
							>
								<el-option :key="index" :label="item.warehouseName" :value="item.id" v-for="(item, index) in warehouseData"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="区号" prop="zoneCode">
							<el-select
								placeholder="请选择区号"
								v-model="state.queryForm.warehouseZoneId"
								style="max-width: 180px"
								clearable
								@change="getColumnSelect"
							>
								<el-option :key="index" :label="item.zoneCode" :value="item.id" v-for="(item, index) in zoneCodeData"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="列号" prop="shelfCode">
							<el-select placeholder="请选择列号" v-model="state.queryForm.shelfCode" style="max-width: 180px" clearable>
								<el-option :key="index" :label="item.shelfCode" :value="item.shelfCode" v-for="(item, index) in columnCodeData"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="巷道位置" prop="locationCode">
							<el-input placeholder="请输入巷道位置" v-model="state.queryForm.locationCode" clearable style="min-width: 240px" />
						</el-form-item>
						<el-form-item>
							<el-button icon="search" type="primary" @click="getDataList"> 查询 </el-button>
						</el-form-item>
					</el-form>
				</div>
				<div class="float-right">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()"> 新 增 </el-button>
					<el-button type="primary" class="ml10" @click="closeClick"> 复位 </el-button>
				</div>
			</div>
		</el-row>
		<el-table
			:data="state.dataList"
			v-loading="state.loading"
			border
			max-height="620px"
			:cell-style="tableStyle.cellStyle"
			:header-cell-style="tableStyle.headerCellStyle"
			@sort-change="sortChangeHandle"
		>
			<el-table-column type="index" label="序号" width="60" />
			<el-table-column prop="warehouseName" label="仓库" show-overflow-tooltip />
			<el-table-column prop="zoneCode" label="区号" show-overflow-tooltip />
			<el-table-column prop="shelfCode" label="列号" show-overflow-tooltip />
			<el-table-column prop="faceCode" label="面" show-overflow-tooltip>
				<template #default="scope">
					{{ scope.row.faceCode == '01' ? '左面' : scope.row.faceCode == '02' ? '右面' : '' }}
				</template>
			</el-table-column>
			<el-table-column label="指示灯类型" show-overflow-tooltip>
				<template #default="scope">
					{{ scope.row.indicatorType == '1' ? '电子标签' : scope.row.indicatorType == '2' ? '巷道灯' : '' }}
				</template>
			</el-table-column>
			<el-table-column prop="locationCode" label="巷道位置" show-overflow-tooltip />

			<el-table-column prop="mac" label="展示灯mac" show-overflow-tooltip />
			<el-table-column label="操作" width="300">
				<template #default="scope">
					<el-button icon="Tickets" text type="primary" @click="handleLog(scope.row)">定位 </el-button>
					<el-button icon="delete" text type="primary" @click="handleDelete(scope.row)">删除 </el-button>
				</template>
			</el-table-column>
		</el-table>
		<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemSysHookMessage">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';

import { delObj, pageList, getArea, getColumn, getWarehouse, locateLight, closeObj } from '/@/api/basicData/locationManagement/lamp';
import { openObj } from '/@/api/basicData/selectionManagement/label';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));

// 定义查询字典
// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
// 多选变量

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: void 0,
		warehouseZoneId: void 0,
		shelfCode: void 0,
		locationCode: void 0,
	},
	pageList,
});
//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 删除操作
const handleDelete = async (row: any) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}
	try {
		await delObj({ wareLocaShelfIndicatorId: row.wareLocaShelfIndicatorId, indicatorType: row.indicatorType });
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
const handleLog = async (row: any) => {
	if (!row.mac) {
		useMessage().error('mac地址不能为空');
		return;
	}
	try {
		row.indicatorType == '1'
			? await openObj({ warehouseId: row.warehouseId, macList: [row.mac] })
			: await locateLight({ warehouseId: row.warehouseId, macList: [row.mac] });
		useMessage().success('定位成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
const closeClick = async () => {
	if (!state.queryForm.warehouseId) return useMessage().error('请选择仓库');
	try {
		await closeObj(state.queryForm.warehouseId);
		useMessage().success('关闭成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
let warehouseData = ref<any[]>([]);
let zoneCodeData = ref<any[]>([]);
let columnCodeData = ref<any[]>([]);
const getWarehouseSelect = async () => {
	warehouseData.value = (await getWarehouse())?.data || [];
};
const getZoneCodeSelect = async (id: string) => {
	state.queryForm.warehouseZoneId = '';
	state.queryForm.shelfCode = '';

	if (!id) return;
	zoneCodeData.value = (await getArea(id))?.data || [];
};
const getColumnSelect = async (id: string) => {
	state.queryForm.shelfCode = '';
	if (!id) return;
	columnCodeData.value = (await getColumn(id))?.data || [];
};
onMounted(() => {
	getWarehouseSelect();
});
</script>
