<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full flex justify-between items-center">
					<div class="flex items-center cursor-pointer" @click="returnClick">
						<img src="/@/assets/back.png" class="w-5 h-5 mr-1" />
						<div class="text-14" style="color: #2e5cf6">返回</div>
					</div>

				</div>
			</el-row>
			<el-row class="mt20">
				<el-col :span="24">
					<Descriptions title="" :column="4" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>




						<DescriptionsItem label="调拨申请单编号">{{detailsData?.billCode}}</DescriptionsItem>
						<DescriptionsItem label="调拨状态">{{detailsData?.billStatus==0?'待审批':detailsData?.billStatus==1?'待出库':detailsData?.billStatus==2?'驳回':detailsData?.billStatus==3?'待接收':detailsData?.billStatus==4?'完成':''}}</DescriptionsItem>
						<DescriptionsItem label="调出仓库">{{detailsData?.outWarehouse}}</DescriptionsItem>
						<DescriptionsItem label="接收仓库">{{detailsData?.entryWarehouse}}</DescriptionsItem>
						<DescriptionsItem label="调拨申请部门">{{detailsData?.applyDept}}</DescriptionsItem>
						<DescriptionsItem label="调拨申请人">{{detailsData?.applyUser}}</DescriptionsItem>
						<DescriptionsItem label="申请时间">{{detailsData?.applyTime}}</DescriptionsItem>
						<DescriptionsItem label="调拨申请原因">{{detailsData?.applyReason}}</DescriptionsItem>
						<DescriptionsItem label="审批部门">{{detailsData?.approveDept}}</DescriptionsItem>
						<DescriptionsItem label="审批人员">{{detailsData?.approveUser}}</DescriptionsItem>
						<DescriptionsItem label="审批时间">{{detailsData?.approveTime}}</DescriptionsItem>
						<DescriptionsItem label="审批结果">{{detailsData?.approveTime}}</DescriptionsItem>
						<DescriptionsItem label="审批意见">{{detailsData?.approveOpinion}}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="detailsData?.billDetailList"
				row-key="userId"
				max-height="calc(100vh - 400px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="materialCode"  show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { getObj } from '/@/api/storage/allocate/applyFor';

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';


const { t } = useI18n();


// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({

});
const { downBlobFile, tableStyle } = useTable(state);

const router = useRouter();

const returnClick = () => {
	router.replace({ path: '/storage/allocate/applyFor/index' });
};
const route=useRoute()
const detailsData = ref<any>();
onMounted(async () => {
	await getObj(route.query?.id).then((res) => {
		detailsData.value = res.data;
	});
});


</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
