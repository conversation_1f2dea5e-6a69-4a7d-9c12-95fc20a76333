<template>
	<div class="layout-padding">
		<splitpanes>
			<pane size="15">
				<div class="layout-padding-auto layout-padding-view">
					<el-scrollbar>
						<query-tree placeholder="请输入物资类别名称查询" :query="deptData.queryList" :show-expand="true" @node-click="handleNodeClick">
							<!-- 没有数据权限提示 -->
							<template #default="{ node, data }">
								<el-tooltip v-if="data.isLock" class="item" effect="dark" :content="$t('sysuser.noDataScopeTip')" placement="right-start">
									<span
										>{{ node.label }}
										<SvgIcon name="ele-Lock" />
									</span>
								</el-tooltip>
								<span v-if="!data.isLock">{{ node.label }}</span>
							</template>
						</query-tree>
					</el-scrollbar>
				</div>
			</pane>
			<pane class="ml8">
				<div class="layout-padding-auto layout-padding-view">
					<el-row>
						<div class="mb8 w-full">
							<div class="float-left">
								<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
									<el-form-item label="仓库" prop="logType">
										<el-select placeholder="请选择仓库" clearable v-model="state.queryForm.warehouseId" class="!w-[160px]">
											<el-option :key="item.id" :label="item.warehouseName" :value="item.id" v-for="item in warehouseData" />
										</el-select>
									</el-form-item>

									<el-form-item label="物资名称" prop="logType">
										<el-input v-model="state.queryForm.materialName" placeholder="请输入物资名称"  />

									</el-form-item>
								</el-form>
							</div>

							<div class="float-right">
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
								<el-button icon="Refresh" @click="exportExcel">导出</el-button>
							</div>
						</div>
					</el-row>

					<el-table
						v-loading="state.loading"
						:data="state.dataList"
						row-key="id"
						border
						:cell-style="tableStyle.cellStyle"
						:header-cell-style="tableStyle.headerCellStyle"
					>
						<el-table-column label="序号" type="index" width="60" fixed="left" />
						<el-table-column label="仓库" prop="warehouseName" fixed="left" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资编码" prop="materialCode" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="800"></el-table-column>
						<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip width="80"></el-table-column>
						<el-table-column label="库存" prop="inventoryNum" show-overflow-tooltip width="80"></el-table-column>
					</el-table>
					<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
				</div>
			</pane>
		</splitpanes>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { materialCategoryTree } from '/@/api/basicData/materialManagement';
import { pageList, getWarehouse, getMaterialName } from '/@/api/storage/stocks';

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useI18n } from 'vue-i18n';

// 动态引入组件
const QueryTree = defineAsyncComponent(() => import('/@/components/QueryTree/index.vue'));

const { t } = useI18n();

const queryRef = ref();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: '',
		materialCatalogId: '',
		materialCategoryId: '',
		parentCategoryId:''
	},
	createdIsNeed: false,
	pageList,

});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 部门树使用的数据
const deptData = reactive({
	queryList: (name: String) => {
		return materialCategoryTree({categoryName:name});
	},
});

// 点击树
const handleNodeClick = (e: any) => {
	state.queryForm.materialCategoryId = e.id;
	state.queryForm.parentCategoryId=e.parentId
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/inventoryInquire/getExportInventoryInquire', Object.assign(state.queryForm, {}), 'users.xlsx');
};

let warehouseData = ref<any>([]);
let options = ref<any>([]);
const getWarehouseData = async () => {
	const res = await getWarehouse();
	warehouseData.value = res.data;
};
const remoteMethod = (query: string) => {
	if (query) {
		setTimeout(() => {
			getMaterialName({ materialName: query }).then((res: any) => {
				const list = res.data;
				options.value = list.filter((item: any) => {
					return item.materialName.toLowerCase().includes(query.toLowerCase());
				});
			});
		}, 1000);
	} else {
		options.value = [];
	}
};

//初始加载
const initData = () => {
	materialCategoryTree({ categoryName: '' }).then((res: any) => {
		if (res.data.length) {
			handleNodeClick(res.data[0])
		 }
	});
}

onMounted(() => {
	initData();
	getWarehouseData();
});
</script>
