<!-- 库房管理 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full" shadow="hover">
					<div class="float-right">
						<el-button
							@click="formDialogRef.openDialog()"
							class="ml10"
							formDialogRef
							icon="folder-add"
							type="primary"
						>
							{{ $t('common.addBtn') }}
						</el-button>
					</div>
				</div>
			</el-row>
			<el-table
				:data="state.dataList"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column :label="t('sysrole.serialNumber')" type="index" width="60" />
				<el-table-column :label="t('appsocial.warehouseNumbers')" prop="warehouseCode" show-overflow-tooltip />
				<el-table-column :label="t('appsocial.warehouseName')" prop="warehouseName" show-overflow-tooltip />
				<el-table-column :label="t('appsocial.department')" prop="deptName" show-overflow-tooltip />
				<el-table-column :label="t('appsocial.manager')" show-overflow-tooltip>
					<template #default="scope">
						{{ scope.row.warehouseAdminNameList.join(',') }}
					</template>
				</el-table-column>
				<el-table-column :label="$t('common.action')" width="150">
					<template #default="scope">
						<el-button icon="edit-pen" @click="formDialogRef.openDialog(scope.row.id)" text type="primary"
							>{{ $t('common.editBtn') }}
						</el-button>
						<el-button icon="delete" @click="handleDelete(scope.row.id)" text type="primary">{{ $t('common.delBtn') }} </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog @refresh="getDataList()" ref="formDialogRef" />
	</div>
</template>

<script lang="ts" name="systemAppSocialDetails" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import { delObj, pageList } from '/@/api/basicData/warehouseManagement';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
const { t } = useI18n();

// 定义变量内容
const formDialogRef = ref();

const state: BasicTableProps = reactive<BasicTableProps>({
	pageList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// 删除操作
const handleDelete = async (id: any) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}
	try {
		await delObj(id);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
</script>
