export default {
	tenant: {
		index: '#',
		importTenantTip: '导入租户',
		id: '租户id',
		name: '租户名称',
		code: '编码',
		tenantDomain: '域名',
		startTime: '开始时间',
		endTime: '结束时间',
		status: '状态',
		delFlag: 'delFlag',
		createBy: '创建人',
		updateBy: '修改人',
		createTime: '创建',
		updateTime: '更新时间',
		menuId: '租户套餐',
		individuationBtn: '个性化',
		inputidTip: '请输入租户id',
		inputnameTip: '请输入名称',
		inputcodeTip: '请输入编码',
		inputtenantDomainTip: '请输入域名',
		inputstartTimeTip: '请输入开始时间',
		inputendTimeTip: '请输入结束时间',
		inputstatusTip: '请输入status',
		inputdelFlagTip: '请输入delFlag',
		inputcreateByTip: '请输入创建人',
		inputupdateByTip: '请输入修改人',
		inputcreateTimeTip: '请输入创建',
		inputupdateTimeTip: '请输入更新时间',
		inputmenuIdTip: '请选择租户套餐',
		deleteDisabledTip: '基础租户不允许删除',
	},
	tenantmenu: {
		name: '套餐',
		index: '#',
		status: '状态',
		createTime: '创建',
	},

	individuation: {
		websiteName: '网站名称',
		miniQr: '移动端二维码',
		logo: '网站图标',
		footerAuthor: '页脚信息',
		background: '登录页背景图',
		inputIndividuationNameTip: '请输入网站名称',
		inputMiniQrTip: '请输入网站图标',
		inputLogoTip: '请输入网站Logo',
		inputFooterAuthorTip: '请输入页脚信息',
		inputBackgroundTip: '请输入登录页背景图',
	},
};
