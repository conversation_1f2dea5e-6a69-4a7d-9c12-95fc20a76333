import request from '/@/utils/request';
//分页查询
export const pageList = (data?: Object) => {
	return request({
		url: '/admin/checkInventory/getCheckInventoryPage',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

export const addObj = (data?: Object) => {
	return request({
		url: '/admin/checkInventory/addCheckInventory',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	});
};

//删除
export function delObj(id?: string) {
	return request({
		url: '/admin/checkInventory/deleteCheckInventoryById/' + id,
		method: 'get',
	});
}
//详情
export const getObj = (id: String) => {
	return request({
		url: '/admin/checkInventory/getCheckInventoryDetailPage' ,
		method: 'get',
	});
};
