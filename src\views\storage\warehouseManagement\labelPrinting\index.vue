<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="仓库" prop="logType">
								<el-select placeholder="请选择仓库" clearable v-model="state.queryForm.warehouseId">
									<el-option :key="item.value" :label="item.warehouseName" :value="item.id" v-for="item in warehouseData" />
								</el-select>
							</el-form-item>
							<el-form-item label="物资名称">
								<el-select
									v-model="state.queryForm.materialCatalogId"
									filterable
									remote
									clearable
									reserve-keyword
									placeholder="请选择物资名称"
									:remote-method="remoteMethod"
									style="width: 240px"
								>
									<el-option v-for="item in options" :key="item.id" :label="item.materialName" :value="item.id" />
								</el-select>
							</el-form-item>
							<el-form-item label="物资条码">
								<div class="flex">
									<el-input v-model="state.queryForm.startMaterialBar" placeholder="例：20250215000100012F000100" clearable class="mr-1 w-[240px]" />
									<span>—</span>
									<el-input v-model="state.queryForm.endMaterialBar" placeholder="20250215000100012F000120" clearable class="ml-1 w-[240px]" />
								</div>
							</el-form-item>
							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button @click="printLabelClick" class="ml10" formDialogRef icon="printer" type="primary"> 打印标签 </el-button>
					</div>
				</div>
			</el-row>

			<el-table
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
				class="w-full"
				row-key="id"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column align="center" type="selection" width="40" />

				<el-table-column label="序号" type="index" width="60" />
				<el-table-column label="物资编码" prop="materialCode" show-overflow-tooltip />
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip />
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="570" />
				<el-table-column label="物资条码" prop="materialBar" show-overflow-tooltip />
				<el-table-column :label="$t('common.action')" width="150">
					<template #default="scope">
						<el-button icon="edit-pen" @click="handleWrtieRFID(scope.row.materialBar)" text type="primary">写标签 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
	</div>
</template>

<script lang="ts" name="systemSysI18n" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import { getWarehouse } from '/@/api/storage/warehouseManagement/other';
import { pageList } from '/@/api/storage/warehouseManagement/labelPrinting';
import { printLabels } from '/@/api/basicData/locationManagement/label';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import axios from 'axios';
import { getConfigurationObj } from '/@/api/storage/rfidSettings';
// @ts-ignore
import { getLodop } from '../../../../../public/plugin/Lodop/LodopFuncs'; //导入模块
import { getMaterialName } from '/@/api/storage/statistics';

// 引入组件
const { t } = useI18n();
// 定义查询字典

// 搜索变量
const queryRef = ref();
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: '',
		materialCatalogId: '',
		startMaterialBar: '',
		endMaterialBar: '',
	},
	createdIsNeed: false,

	pageList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 多选事件
const handleSelectionChange = (objs: { materialCode: string; materialIdentify: string; materialBar: string }[]) => {
	selectObjs.value = objs.map(({ materialCode, materialIdentify, materialBar }) => ({ materialCode, materialIdentify, materialBar }));
	multiple.value = !objs.length;
};

//获取详情
let Ip = ref('');
let Port = ref('');
let sign = ref('');
let printMode = ref<any>('');

const getConfiguration = () => {
	getConfigurationObj().then((res: any) => {
		Ip.value = res.data.rfidDeviceWorkbenchIp;
		Port.value = res.data.port;
		printMode.value = res.data.printingEquipment;

		sign.value = res.data.materialLabel;
	});
};
// 写标签
const handleWrtieRFID = async (codeNum: any) => {
	const RFIDurl = 'http://' + Ip.value + ':' + Port.value;
	let formData = { cmd: '10007', data: { writeCount: 1, oldepc: '', newepc: codeNum } };
	axios.post(RFIDurl, formData).then((res: any) => {
		let status = res.data.data.status;
		if (status == 0) {
			useMessage().success(t('标签写入成功'));
		} else if (status == -1) {
			useMessage().success(t('正在读卡中'));
		} else if (status == -2) {
			useMessage().success(t('参数错误'));
		} else if (status == -3) {
			useMessage().success(t('epc长度错误'));
		} else if (status == 250) {
			useMessage().success(t('有电子标签，但通讯不畅'));
		} else if (status == 251) {
			useMessage().success(t('无电子标签'));
		} else if (status == 253) {
			useMessage().success(t('命令长度错误'));
		} else {
			useMessage().success(t(`错误编码：${status}`));
		}
	});
};
//打印物资标签
const printLabelClick = async () => {
	if (printMode.value === 3) {
		return useMessage().error('请先配置打印设备');
	}
	try {
		let itemsToPrint: any = [];
		if (selectObjs.value.length) {
			itemsToPrint = selectObjs.value;
		} else {
			await useMessageBox().confirm('是否打印当前页全部标签?');
			itemsToPrint = state.dataList.map((item: any) => ({
				materialCode: item.materialCode,
				materialIdentify: item.materialIdentify,
				materialBar: item.materialBar,
			}));
		}
		if (printMode.value == 2) {
			itemsToPrint.forEach((item: any) => printingOperation(item));
		} else if (printMode.value == 1) {
			await rfidPrint(itemsToPrint);
		}
	} catch (error) {
		console.error('打印操作失败:', error);
	}
};

//rfid 打印货位标签
const rfidPrint = async (obj: any) => {
	try {
		await printLabels(obj);
		useMessage().success('打印成功');
	} catch (error: any) {
		useMessage().error(error.msg);
	}
};
//打印物资标签
const printingOperation = (obj: any) => {
	const userAgent = navigator.userAgent.toLowerCase();
	let LODOP = getLodop(); //调用 getLodop获取LODOP对象
	LODOP.PRINT_INITA(0, 0, '99mm', '15mm', '打印二维码');
	LODOP.SET_PRINT_PAGESIZE(1, '99mm', '15mm', ''); //设定纸张大小

	if (userAgent.indexOf('win') !== -1) {
		LODOP.SET_LICENSES('', 'AF8A5800B823915C50BCF67B925E8EA7', '', '');
		if (sign.value == '1') {
			LODOP.ADD_PRINT_TEXT('4mm', '3mm', '65mm', '7mm', obj.materialCode);
		} else if (sign.value == '2') {
			LODOP.ADD_PRINT_TEXT('4mm', '3mm', '65mm', '7mm', obj.materialIdentify);
		}
		LODOP.SET_PRINT_STYLEA(0, 'FontSize', 11);

		LODOP.ADD_PRINT_TEXT('10mm', '3mm', '70mm', '8mm', obj.materialBar);
		LODOP.SET_PRINT_STYLEA(0, 'FontSize', 11);
		LODOP.SET_PRINT_STYLEA(0, 'TextNeatRow', true);
		LODOP.ADD_PRINT_BARCODE('1.6mm', '70mm', '14.510mm', '14.510mm', 'QRCode', obj.materialBar);
		LODOP.SET_PRINT_STYLEA(0, 'QRCodeVersion', 2);
		LODOP.SET_PRINT_STYLEA(0, 'QRCodeErrorLevel', 'L');
		LODOP.PRINT();
	}
	if (userAgent.indexOf('linux') !== -1) {
		LODOP.SET_LICENSES('', 'E9663FC92B893B0D4F484B', '', '');

		if (sign.value == '1') {
			LODOP.ADD_PRINT_TEXT('0.2mm', '3mm', '65mm', '7mm', obj.materialCode);
		} else if (sign.value == '2') {
			LODOP.ADD_PRINT_TEXT('0.2mm', '3mm', '65mm', '7mm', obj.materialIdentify);
		}
		LODOP.SET_PRINT_STYLEA(0, 'FontSize', 10);

		LODOP.ADD_PRINT_TEXT('5mm', '3mm', '70mm', '8mm', obj.materialBar);
		LODOP.SET_PRINT_STYLEA(0, 'FontSize', 10);
		LODOP.SET_PRINT_STYLEA(0, 'TextNeatRow', true);
		LODOP.ADD_PRINT_BARCODE('0.1mm', '70mm', '11.5mm', '11.5mm', 'QRCode', obj.materialBar);
		LODOP.SET_PRINT_STYLEA(0, 'QRCodeVersion', 2);
		LODOP.SET_PRINT_STYLEA(0, 'QRCodeErrorLevel', 'L');
		LODOP.PRINT();
	}
};

let options = ref<any>([]);

const remoteMethod = (query: string) => {
	if (query) {
		setTimeout(() => {
			getMaterialName({ materialName: query }).then((res: any) => {
				const list = res.data;
				options.value = list.filter((item: any) => {
					return item.materialName.toLowerCase().includes(query.toLowerCase());
				});
			});
		}, 1000);
	} else {
		options.value = [];
	}
};
// 获取仓库数据
const warehouseData = ref<any[]>([]);
const getWarehouseData = () => {
	getWarehouse().then((res: any) => {
		warehouseData.value = res.data;
	});
};

onMounted(() => {
	getWarehouseData();

	getConfiguration();
});
</script>
