import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};

export const pageList = (obj?: Object) => {
	return request({
		url: '/admin/entryWarehouseBill/purchase/getEntryWarehouseBillPage',
		method: 'post',
		data: obj,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};
//新增
export const addObj = (data?: Object) => {
	return request({
		url: '/admin/entryWarehouseBill/purchase/addEntryWarehouseBill',
		method: 'post',
		headers: {
			'Content-Type': 'application/json',
		},
		data,
	});
};
export const putObj = (data?: Object) => {
	return request({
		url: '/admin/entryWarehouseBill/purchase/updateEntryWarehouseBill',
		method: 'post',
		data,
	});
};
//删除
export const delObj = (id?: string) => {
	return request({
		url: '/admin/entryWarehouseBill/purchase/deleteEntryWarehouseBillById/' + id,
		method: 'get',
	});
};
//核验
export const checkObj = (id?: any) => {
	return request({
		url: '/admin/entryWarehouseBill/purchase/verifyEntryWarehouseBillById/' + id,
		method: 'get',
	});
};
//核验
export const checkObjs = (id?: any) => {
	return request({
		url: '/admin/entryWarehouseBill/transfer/verifyEntryWarehouseBillById/' + id,
		method: 'get',
	});
};
//查看  详情
export const getObj = (id?: any) => {
	return request({
		url: '/admin/entryWarehouseBill/getEntryWarehouseBillById/' + id,
		method: 'get',
	});
};

export const getMxObj = (id?: any) => {
	return request({
		url: '/admin/entryWarehouseBillDetail/getEntryWarehouseBillDetailById/' + id,
		method: 'get',
	});
};

//pdf 设置返回格式
export const downPdf = (id?: any) => {
	return request({
		url: '/admin/entryWarehouseBill/pdfEntryWarehouseBillById/' + id,
		method: 'get',
		responseType: 'blob',
	});
};
//出库单明细
export const getMxObjs = (id?: any) => {
	return request({
		url: '/admin/outWarehouseBill/getOutWarehouseBillDetailById/' + id,
		method: 'get',
	});
};
