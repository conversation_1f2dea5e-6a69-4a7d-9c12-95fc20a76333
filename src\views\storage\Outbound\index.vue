<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="出库单编号">
								<el-input v-model="state.queryForm.billCode" placeholder="请输入出库单编号" clearable class="!max-w-[180px]" />
							</el-form-item>
							<el-form-item label="出库单状态">
								<el-select placeholder="请选择出库单状态" clearable v-model="state.queryForm.billStatus" class="!max-w-[180px]">
									<el-option
										:key="item.value"
										:label="item.label"
										:value="item.value"
										v-for="item in [
											{ label: '待确认', value: 0 },
											{ label: '待出库', value: 1 },
											{ label: '已出库', value: 2 },
										]"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="申请部门" prop="deptId">
								<el-tree-select
									:data="deptData"
									:props="{ value: 'id', label: 'name', children: 'children' }"
									check-strictly
									class="!max-w-[180px]"
									clearable
									placeholder="请选择申请部门"
									@change="handleDepartmentChange"
									v-model="state.queryForm.applyDeptId"
								>
								</el-tree-select>
							</el-form-item>
							<el-form-item label="申请人员" prop="warehouseAdminIdList">
								<el-select placeholder="请选择申请人员" v-model="state.queryForm.applyUserId" class="!max-w-[180px]" clearable>
									<el-option :key="index" :label="item.name" :value="item.userId" v-for="(item, index) in peopleData"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="预计归还时间" prop="time">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="-"
									type="daterange"
									@change="dateChange"
									v-model="state.queryForm.time"
									value-format="YYYY-MM-DD HH:mm:ss"
									format="YYYY-MM-DD"
								/>
							</el-form-item>

							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button icon="folder-add" type="primary" @click="routerClick()">
							{{ $t('common.addBtn') }}
						</el-button>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="出库单编号" prop="billCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="出库单状态" show-overflow-tooltip>
					<template #default="scope">
						{{ billStatus[scope.row.billStatus] }}
					</template>
				</el-table-column>
				<el-table-column label="出库仓库" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="出库用途" prop="outPurpose" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请部门" prop="applyDept" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请人员" prop="applyUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="是否归还" show-overflow-tooltip>
					<template #default="scope">
						{{ scope.row.isReturn == '1' ? '是' : scope.row.isReturn == '0' ? '否' : '' }}
					</template>
				</el-table-column>
				<el-table-column label="预计归还时间" prop="planReturnTime" show-overflow-tooltip></el-table-column>
				<el-table-column label="创建人员" prop="createUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="创建时间" prop="createTime" show-overflow-tooltip></el-table-column>
				<el-table-column label="出库人员" prop="outUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="出库时间" prop="outTime" show-overflow-tooltip></el-table-column>

				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 确认 confirm-->
						<el-button icon="" text type="primary" @click="confirmClick(scope.row.id)" v-if="scope.row.billStatus == '0'"> 确认 </el-button>
						<!-- 修改  add-->
						<el-button icon="edit-pen" text type="primary" @click="routerClick(scope.row.id)" v-if="scope.row.billStatus =='0'">
							{{ $t('common.editBtn') }}
						</el-button>
						<!-- 删除 -->
						<el-button icon="delete" @click="handleDelete(scope.row.id)" text type="primary" v-if="scope.row.billStatus == '0'"
							>{{ $t('common.delBtn') }}
						</el-button>

						<!-- 拣货 picking -->
						<el-button text type="primary" @click="pickingClick(scope.row.id)" v-if="scope.row.billStatus == '1'"> 拣货 </el-button>
						<!-- 出库  outbound-->
						<el-button text type="primary" @click="outboundClick(scope.row.id)" v-if="scope.row.billStatus == '1'"> 出库 </el-button>

						<!-- 查看  form-->
						<el-button text type="primary" @click="formClick(scope.row.id)" v-if="scope.row.billStatus == '2'"> 查看 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { pageList, getPeople, delObj } from '/@/api/storage/outbound';
import { deptTree } from '/@/api/admin/dept';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();
const deptData = ref<any[]>([]);
const peopleData = ref<any[]>([]);

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		billCode: '',
		billStatus: '',
		applyDeptId: '',
		applyUserId: '',
		beginPlanReturnTime: '',
		endPlanReturnTime: '',
		time: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

const dateChange = (value: any) => {
	state.queryForm.beginPlanReturnTime = '';
	state.queryForm.endPlanReturnTime = '';
	if (!Array.isArray(value)) return;
	state.queryForm.beginPlanReturnTime = value[0];
	state.queryForm.endPlanReturnTime = value[1].replace(/\d{2}:\d{2}:\d{2}$/, '23:59:59');
};

// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(id);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

//查看入库单页面
const formClick = (id?: any) => {
	router.push({
		path: '/storage/Outbound/form',
		query: { id: id, notCreateTags: 'true' },
	});
};
//出库
const outboundClick = (id?: any) => {
	router.push({
		path: '/storage/Outbound/outbound',
		query: { id: id, notCreateTags: 'true' },
	});
};
//拣货
const pickingClick = (id?: any) => {
	router.push({
		path: '/storage/Outbound/picking',
		query: { id: id, notCreateTags: 'true' },
	});
};
//确认
const confirmClick = (id?: any) => {
	router.push({
		path: '/storage/Outbound/confirm',
		query: { id: id, notCreateTags: 'true' },
	});
};

const billStatus = ref<any>({
	'0': '待确认',
	'1': '待出库',
	'2': '已出库',
});

//新增 修改页面
const router = useRouter();
const routerClick = (id?: any) => {
	const tagsViewName = id ? '出库管理修改' : '出库管理新增';
	router.push({
		path: '/storage/Outbound/add',
		query: { id: id, tagsViewName: tagsViewName, notCreateTags: 'true' },
	});
};

// 部门变更时清空管理人员
const handleDepartmentChange = async (id: any) => {
	if (!id) {
		state.queryForm.applyDeptId = '';
		peopleData.value = [];

		return;
	} else {
		await getPeopleData(id);
	}
};

// 初始化部门数据
const getDeptData = () => {
	deptTree().then((res: any) => {
		deptData.value = res.data;
	});
};
// 获取部门下人员
const getPeopleData = async (id: any) => {
	const { records } = (await getPeople({ deptId: id })).data;
	peopleData.value = records;
};

onMounted(() => {
	getDeptData();
});
</script>
