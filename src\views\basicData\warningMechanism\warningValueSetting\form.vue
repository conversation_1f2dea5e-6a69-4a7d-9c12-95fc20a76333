<template>
	<el-dialog :close-on-click-modal="false" :title="form.id ? $t('common.editBtn') : $t('common.addBtn')" width="600" draggable v-model="visible">
		<el-form :model="form" :rules="dataRules" label-width="100px" ref="dataFormRef">
			<el-form-item :label="$t('sysrole.warehouse')" prop="warehouseId">
				<el-select :placeholder="$t('sysrole.warehouseReminder')" clearable v-model="form.warehouseId" :disabled="form.id !== ''">
					<el-option :key="item.value" :label="item.warehouseName" :value="item.id" v-for="item in warehouseData" />
				</el-select>
			</el-form-item>
			<el-form-item label="物资名称" prop="materialCatalogId">
				<el-select
					:disabled="form.id !== ''"
					v-model="form.materialCatalogId"
					clearable
					filterable
					remote
					reserve-keyword
					placeholder="请输入物资名称"
					:remote-method="remoteMethod"
				>
					<el-option v-for="item in options" :key="item.id" :label="item.materialName" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item :label="$t('sysrole.inventoryWarningValue')" prop="threshold">
				<el-input-number v-model="form.threshold" :min="0" controls-position="right" />
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
				<el-button @click="onSubmit" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script lang="ts" name="systemRoleDialog" setup>
import { useMessage } from '/@/hooks/message';
import { addObj, putObj, getWarehouse, getMaterialName, getObj } from '/@/api/basicData/warningMechanism';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
//引入字典

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 提交表单数据
const form = reactive({
	id: '',
	warehouseId: '',
	materialCatalogId: '',
	threshold: '',
});

// 定义校验规则
const dataRules = ref({
	warehouseId: [{ required: true, message: '仓库不能为空', trigger: 'change' }],
	materialCatalogId: [{ required: true, message: '物资名称不能为空', trigger: 'change' }],
	threshold: [
		{
			required: true,
			message: '库存预警值不能为空',
			trigger: 'change',
		},
		{
			pattern: /^[0-9]+$/,
			message: '值只能是数字',
			trigger: 'change',
		},
	],
});

// 打开弹窗
const openDialog = (id: String) => {
	visible.value = true;
	form.id = '';
	nextTick(() => {
		dataFormRef.value.resetFields();
	});

	if (id) {
		getappSocialDetailsData(id);
	}
};
// 初始化表单数据
const getappSocialDetailsData = async(id: any) => {
	// 获取数据
	let res = await getObj(id)
	Object.assign(form, res.data, { materialCatalogId: res.data.materialName })
};
// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
		const { id, ...others } = form;
		form.id ? await putObj({ id, threshold: form.threshold }) : await addObj(others);
		useMessage().success(t(form.id ? 'common.editSuccessText' : 'common.addSuccessText'));
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
let warehouseData = ref<any>([]);

const options = ref<any[]>([]);
const remoteMethod = (query: string) => {
	if (query) {
		setTimeout(() => {
			getMaterialName({ materialName: query }).then((res) => {
				const list = res.data;
				options.value = list.filter((item: any) => {
					return item.materialName.toLowerCase().includes(query.toLowerCase());
				});
			});
		}, 1000);
	} else {
		options.value = [];
	}
};
onMounted(() => {
	getWarehouse().then((res) => {
		warehouseData.value = res.data;
	});
});
</script>
