export default {
	job: {
		index: '#',
		jobLogBtn: '日志',
		jobStartBtn: '启动',
		jobRunBtn: '执行',
		jobPauseBtn: '暂停',
		importsysJobTip: '导入定时任务调度表',
		jobId: '任务id',
		jobName: '任务名称',
		jobGroup: '任务组名',
		jobOrder: '组内执行顺利',
		jobType: '类型',
		executePath: '执行路径',
		className: '执行文件',
		methodName: '执行方法',
		methodParamsValue: '参数值',
		cronExpression: 'cron表达式',
		misfirePolicy: '错失执行策略',
		jobTenantType: '1、多租户任务;2、非多租户任务',
		jobStatus: '任务状态',
		jobExecuteStatus: '执行状态',
		createBy: '创建者',
		createTime: '创建时间',
		updateBy: '更新者',
		updateTime: '更新时间',
		startTime: '初次执行时间',
		previousTime: '上次执行时间',
		nextTime: '下次执行时间',
		tenantId: '租户',
		remark: '备注信息',
		jobMessage: '日志信息',
		jobLogStatus: '执行状态',
		executeTime: '执行时间',
		exceptionInfo: '异常信息',
		inputjobIdTip: '请输入任务id',
		inputjobNameTip: '请输入任务名称',
		inputjobGroupTip: '请输入任务组名',
		inputjobOrderTip: '请输入组内执行顺利',
		inputjobTypeTip: '请输入类型',
		inputexecutePathTip: '请输入请求地址',
		inputclassNameTip: '请输入类全路径',
		inputBeanNameTip: '请输入Bean名称',
		inputmethodNameTip: '请输入方法名称',
		inputmethodParamsValueTip: '请输入参数值',
		inputcronExpressionTip: '请输入cron表达式',
		inputmisfirePolicyTip: '请输入错失执行策略',
		inputjobTenantTypeTip: '请输入1、多租户任务;2、非多租户任务',
		inputjobStatusTip: '请输入任务状态',
		inputjobExecuteStatusTip: '请输入执行状态',
		inputcreateByTip: '请输入创建者',
		inputcreateTimeTip: '请输入创建时间',
		inputupdateByTip: '请输入更新者',
		inputupdateTimeTip: '请输入更新时间',
		inputstartTimeTip: '请输入初次执行时间',
		inputpreviousTimeTip: '请输入上次执行时间',
		inputnextTimeTip: '请输入下次执行时间',
		inputtenantIdTip: '请输入租户',
		inputremarkTip: '请输入备注信息',
		jobType1ClassName: '类全路径',
		jobType1MethodName: '方法名称',
		jobType2ClassName: 'Bean名称',
		jobType2MethodName: '方法名称',
		jobType3ExecutePath: '请求地址',
		jobType4ExecutePath: 'Jar包路径',
		singleParam: '只支持单个参数',
	},
	log: {
		index: '#',
		importsysJobLogTip: '导入定时任务执行日志表',
		jobLogId: '任务日志ID',
		jobId: '任务id',
		jobName: '任务名称',
		jobGroup: '任务组名',
		jobMessage: '日志信息',
		jobLogStatus: '执行状态',
		executeTime: '执行时间',
		exceptionInfo: '异常信息',
		createTime: '创建时间',
		tenantId: '租户id',
		inputJobLogIdTip: '请输入任务日志ID',
		inputJobIdTip: '请输入任务id',
		inputJobNameTip: '请输入任务名称',
		inputJobGroupTip: '请输入任务组名',
		inputMethodNameTip: '请输入任务方法',
		inputMethodParamsValueTip: '请输入参数值',
		inputCronExpressionTip: '请输入cron执行表达式',
		inputJobMessageTip: '请输入日志信息',
		inputJobLogStatusTip: '请输入执行状态',
		inputExecuteTimeTip: '请输入执行时间',
		inputExceptionInfoTip: '请输入异常信息',
		inputCreateTimeTip: '请输入创建时间',
		inputTenantIdTip: '请输入租户id',
	},
};
