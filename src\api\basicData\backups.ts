import request from '/@/utils/request';


export const pageList = () => {
	return request({
		url: '/admin/dataBackup/listFile',
		method: 'get',
	});
};
//验证密码
export const verifyPassWord = (password?: string) => {
	return request({
		url: '/admin/dataBackup/recoverVerify/' + password,
		method: 'get',
	});
};
//恢复数据
export const restore = (file?: string) => {
	return request({
		url: '/admin/dataBackup/recover/' + file,
		method: 'get',
	});
};
//备份数据
export const backupObj = () => {
	return request({
		url: '/admin/dataBackup/backup',
		method: 'get',
	});
};
