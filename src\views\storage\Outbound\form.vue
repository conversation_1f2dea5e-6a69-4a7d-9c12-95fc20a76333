<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full flex justify-between items-center">
					<div class="flex items-center cursor-pointer" @click="returnClick">
						<img src="/@/assets/back.png" class="w-5 h-5 mr-1" />
						<div class="text-14" style="color: #2e5cf6">返回</div>
					</div>
					<div>
						<el-button @click="exportPdf">导出</el-button>
					</div>
				</div>
			</el-row>
			<el-row class="mt20">
				<el-col :span="24">
					<Descriptions title="出库单" :column="4" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>


						<DescriptionsItem label="出库单编号">{{detailsData?.billCode}}</DescriptionsItem>
						<DescriptionsItem label="出库仓库">{{detailsData?.warehouseName}}</DescriptionsItem>
						<DescriptionsItem label="出库单状态">{{detailsData?.billStatus==0?'待确认':detailsData?.billStatus==1?'待出库':detailsData?.billStatus==2?'已出库':''}}</DescriptionsItem>

						<DescriptionsItem label="出库用途">{{detailsData?.outPurpose}}</DescriptionsItem>
						<DescriptionsItem label="是否归还">{{detailsData?.needReturn==1?'是':detailsData?.needReturn==0?'否':''}}</DescriptionsItem>
						<DescriptionsItem label="预计归还时间" v-if="detailsData?.needReturn==1">{{detailsData?.planReturnTime}}</DescriptionsItem>
						<DescriptionsItem label="申请部门">{{detailsData?.applyDept}}</DescriptionsItem>
						<DescriptionsItem label="申请人员">{{detailsData?.applyUser}}</DescriptionsItem>
						<DescriptionsItem label="创建人">{{detailsData?.createUser}}</DescriptionsItem>
						<DescriptionsItem label="创建时间">{{detailsData?.createTime}}</DescriptionsItem>
						<DescriptionsItem label="出库人">{{detailsData?.outUser}}</DescriptionsItem>
						<DescriptionsItem label="出库时间">{{detailsData?.outTime}}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="detailsData?.billDetailVOList"

				row-key="userId"
				max-height="calc(100vh - 400px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" width="200" fixed="right">
					<template #default="scope">
						<!-- 修改信息 -->
						<el-button text type="primary" @click="LogDetailRef.openDialog(scope.row)"> 明细 </el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<log-detail ref="LogDetailRef"></log-detail>
	</div>
</template>

<script lang="ts" setup>
import { getObj } from '/@/api/storage/outbound';

const LogDetail = defineAsyncComponent(() => import('./detail.vue'));

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
const route = useRoute();

// 动态引入组件
const LogDetailRef = ref();

const { t } = useI18n();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});
const { downBlobFile, tableStyle } = useTable(state);

const detailsData = ref<any>();
onMounted(async () => {
	await getObj(route.query?.id).then((res) => {
		detailsData.value = res.data;
	});
});
const exportPdf = async () => {
	downBlobFile(`/admin/outWarehouseBill/pdfOutWarehouseBillById/${route.query?.id}`, '', '出库单.pdf');
};
const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/Outbound/index' });
};


</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
