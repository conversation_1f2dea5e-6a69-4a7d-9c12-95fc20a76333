<template>
	<div>
		<div class="layout-padding-auto layout-padding-view">
			<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="95px">
				<el-form-item :label="t('sysrole.groupName')" prop="enableStatus">
					<el-radio-group v-model="form.enableStatus">
						<el-radio :key="index" :label="item.value" border v-for="(item, index) in enable_status">{{ item.label }} </el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item :label="$t('sysrole.templateType')" prop="warningMethod">
					<el-select v-model="form.warningMethod" :placeholder="$t('sysrole.selectType')" clearable multiple class="w-80">
						<el-option v-for="item in warning_method" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="过期预警值" prop="warningValue">
					<div class="flex">
						<div class="mr-2">提前</div>
						<div>
							<el-input v-model="form.warningValue" class="w-20" />
						</div>
						<div class="ml-2">天提醒</div>
					</div>
				</el-form-item>
				<el-form-item>
					<el-button @click="onSubmits" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts" name="systemSysMessage">
import { getExpiredObj, putExpiredObj } from '/@/api/basicData/warningMechanism';
import { useMessage} from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';
const { enable_status } = useDict('enable_status');

const { warning_method } = useDict('warning_method');
// 引入组件
const { t } = useI18n();

// 定义变量内容
const dataFormRef = ref();
const loading = ref(false);

// 提交表单数据
const form = ref<any>({
	enableStatus: '1',
	warningMethod: ['5'],
	warningValue: '0',
});

// 定义校验规则
const dataRules = ref({
	enableStatus: [{ required: true, message: '请选择', trigger: 'blur' }],
	warningMethod: [{ required: true, message: '请选择通知方式', trigger: 'blur' }],
	warningValue: [
		{
			required: true,
			message: '过期预警值不能为空',
			trigger: 'change',
		},
		{
			pattern: /^[0-9]+$/,
			message: '值只能是数字',
			trigger: 'blur',
		},
	],
});
//获取数据
const getInfos = async () => {
	try {
		loading.value = true;
		let res = await getExpiredObj();
		if (!res.data) return;
		form.value.enableStatus = String(res.data.enableStatus);
		form.value.warningMethod = String(res.data.warningMethod).split(',');
		form.value.warningValue = String(res.data.warningValue);
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};
// 提交
const onSubmits = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;
		await putExpiredObj({ ...form.value });
		useMessage().success('操作成功');
		getInfos();
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};
onMounted(() => {
	getInfos();
});
</script>
