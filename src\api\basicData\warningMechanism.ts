import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};
//物资名称 联想
export const getMaterialName = (params?: Object) => {
	return request({
		url: '/admin/materialCatalog/getMaterialName',
		method: 'get',
		params,
	});
};
export const pageList = (params?: Object) => {
	return request({
		url: '/admin/inventoryWarnConf/getInventoryWarnConfPage',
		method: 'get',
		params,
	});
};
export const addObj = (data?: Object) => {
	return request({
		url: '/admin/inventoryWarnConf/AddInventoryWarnConf',
		method: 'post',
		data,
	});
};
export const putObj = (data?: Object) => {
	return request({
		url: '/admin/inventoryWarnConf/updateInventoryWarnConf',
		method: 'post',
		data,
	});
};

export const delObj = (id?: string) => {
	return request({
		url: '/admin/inventoryWarnConf/deleteInventoryWarnConfById/' + id,
		method: 'get',
	});
};
export const downObj = () => {
	return request({
		url: '/admin/inventoryWarnConf/download',
		method: 'get',
	});
};
export const importObj = () => {
	return request({
		url: '/admin/inventoryWarnConf/import',
		method: 'post',
	});
};
export const getInventoryObj = () => {
	return request({
		url: '/admin/inventoryWarnConf/GetAlertConfiguration',
		method: 'get',
	});
};
export const putInventoryObj = (obj: Object) => {
	return request({
		url: '/admin/inventoryWarnConf/updateAlertConfiguration',
		method: 'post',
		data: obj,
	});
};

export const getExpiredObj = () => {
	return request({
		url: '/admin/inventoryWarnConf/getDictionaryWarnings',
		method: 'get',
	});
};
export const putExpiredObj = (obj: Object) => {
	return request({
		url: '/admin/inventoryWarnConf/updateDictionaryWarnings',
		method: 'post',
		data: obj,
	});
};
export const getUnreturnedObj = () => {
	return request({
		url: '/admin/inventoryWarnConf/getNotAlert',
		method: 'get',
	});
};
export const putUnreturnedObj = (obj: Object) => {
	return request({
		url: '/admin/inventoryWarnConf/updateNotAlert',
		method: 'post',
		data: obj,
	});
};

export const getObj = (id?: string) => {
	return request({
		url: '/admin/inventoryWarnConf/getInventoryWarnConfById/' + id,
		method: 'get',
	});
};
