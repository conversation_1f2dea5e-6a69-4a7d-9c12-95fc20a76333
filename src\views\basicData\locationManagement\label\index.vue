<template>
	<div>
		<el-row>
			<div class="mb8 w-full">
				<div class="float-left">
					<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
						<el-form-item label="所属仓库" prop="warehouseId">
							<el-select
								placeholder="请选择所属仓库"
								v-model="state.queryForm.warehouseId"
								style="max-width: 180px"
								clearable
								@change="getZoneCodeSelect"
							>
								<el-option :key="index" :label="item.warehouseName" :value="item.id" v-for="(item, index) in warehouseData"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="区号" prop="zoneCode">
							<el-select
								placeholder="请选择区号"
								v-model="state.queryForm.warehouseZoneId"
								style="max-width: 180px"
								clearable
								@change="getColumnSelect"
							>
								<el-option :key="index" :label="item.zoneCode" :value="item.id" v-for="(item, index) in zoneCodeData"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="列号" prop="shelfCode">
							<el-select placeholder="请选择列号 " v-model="state.queryForm.shelfCode" style="max-width: 180px" clearable>
								<el-option :key="index" :label="item.shelfCode" :value="item.shelfCode" v-for="(item, index) in columnCodeData"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="位置编码" prop="locationCode">
							<el-input placeholder="请输入位置编码" v-model="state.queryForm.locationCode" clearable style="min-width: 240px" />
						</el-form-item>
						<el-form-item>
							<el-button icon="search" type="primary" @click="getDataList"> 查询 </el-button>
						</el-form-item>
					</el-form>
				</div>
				<div class="float-right">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()"> 新 增 </el-button>
					<el-button type="primary" class="ml10" @click="autoBindingClick"> 自动绑定 </el-button>
					<el-button type="primary" class="ml10" @click="printLabelClick"> 打印标签 </el-button>
				</div>
			</div>
		</el-row>
		<el-table
			:data="state.dataList"
			v-loading="state.loading"
			border
			:cell-style="tableStyle.cellStyle"
			:header-cell-style="tableStyle.headerCellStyle"
			@selection-change="selectionChangHandle"
			@sort-change="sortChangeHandle"
			max-height="620px"
		>
			<el-table-column type="selection" width="40" align="center" />
			<el-table-column type="index" label="序号" width="60" />
			<el-table-column prop="warehouseName" label="仓库" show-overflow-tooltip />
			<el-table-column prop="zoneCode" label="区号" show-overflow-tooltip />
			<el-table-column prop="shelfCode" label="列号" show-overflow-tooltip />
			<el-table-column prop="locationCode" label="位置编码" show-overflow-tooltip />
			<el-table-column prop="locationBar" label="位置条码" show-overflow-tooltip />
			<el-table-column prop="mac" label="标签mac" show-overflow-tooltip />
			<el-table-column label="操作" width="300">
				<template #default="scope">
					<el-button icon="edit-pen" text type="primary" @click="formDialogRef.openDialog(scope.row.warehouseLocationId)">绑定 </el-button>
					<el-button icon="delete" text type="primary" @click="handleDelete(scope.row.warehouseLocationId)">删除 </el-button>
					<el-button icon="Tickets" text type="primary" @click="handleLog(scope.row.mac, scope.row.warehouseId)">定位 </el-button>
				</template>
			</el-table-column>
		</el-table>
		<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemSmsMessage">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { delObj, pageList, getArea, getColumn, getWarehouse, autoBind, locateLight,printLabel } from '/@/api/basicData/locationManagement/label';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { getConfigurationObj } from '/@/api/storage/rfidSettings';

// @ts-ignore
import { getLodop } from '../../../../../public/plugin/Lodop/LodopFuncs';

import { useI18n } from 'vue-i18n';
const { t } = useI18n();

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: '',
		warehouseZoneId: '',
		shelfCode: '',
		locationCode: '',
	},
	createdIsNeed: false,
	pageList,
});

const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);

// 多选事件
const selectionChangHandle = (objs: { locationCode: string; locationBar: string }[]) => {
	selectObjs.value = objs.map(({ locationCode, locationBar }) => ({
		locationCode,
		locationBar,
	}));
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(id);
		getDataList(false);
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

const handleLog = async (mac: string, id: string) => {
	if (!mac) {
		useMessage().error('mac地址不能为空');
		return;
	}
	try {
		await locateLight({ warehouseId: id, macList: [mac] });
		useMessage().success('定位成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
let warehouseData = ref<any[]>([]);
let zoneCodeData = ref<any[]>([]);
let columnCodeData = ref<any[]>([]);
const getWarehouseSelect = async () => {
	warehouseData.value = (await getWarehouse())?.data || [];
};
const getZoneCodeSelect = async (id: string) => {
	state.queryForm.warehouseZoneId = '';
	state.queryForm.shelfCode = '';
	if (!id) return;
	zoneCodeData.value = (await getArea(id))?.data || [];
};
const getColumnSelect = async (id: string) => {
	state.queryForm.shelfCode = '';
	if (!id) return;
	columnCodeData.value = (await getColumn(id))?.data || [];
};

const autoBindingClick = async () => {
	try {
		await useMessageBox().confirm('此操作将自动绑定');
	} catch {
		return;
	}
	try {
		autoBind({
			warehouseId: state.queryForm.warehouseId,
			warehouseZoneId: state.queryForm.warehouseZoneId,
			shelfCode: state.queryForm.shelfCode,
			locationCode: state.queryForm.locationCode,
		}).then(() => {
			getDataList();
		});
		useMessage().success(t('common.optSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
const printLabelClick = async () => {
  if (printMode.value === 3) {
    return useMessage().error('请先配置打印设备');
  }
  try {
    let itemsToPrint: any = [];
    if (selectObjs.value.length) {
      itemsToPrint = selectObjs.value;
    } else {
      await useMessageBox().confirm('是否打印当前页全部标签?');
		 itemsToPrint =state.dataList.map((item:any) => ({locationCode:item.locationCode,locationBar:item.locationBar}));
    }
    if (printMode.value == 2) {
      itemsToPrint.forEach((item:any) => printingOperation(item));
    } else if (printMode.value == 1) {
     await rfidPrint(itemsToPrint);
    }

  } catch (error) {
    console.error('打印操作失败:', error);
  }
};
//rfid 打印货位标签
const rfidPrint = async (obj: any) => {
  try {
    await printLabel(obj);
    useMessage().success('打印成功');
  } catch (error:any) {
    useMessage().error(error.msg);
  }
};
//打印货位标签
const printingOperation = (obj: any) => {
	const userAgent = navigator.userAgent.toLowerCase();
	let LODOP = getLodop(); //调用 getLodop获取LODOP对象
	LODOP.PRINT_INITA(0, 0, '100mm', '15mm', '打印二维码');
	LODOP.SET_PRINT_PAGESIZE(1, '100mm', '15mm', ''); //设定纸张大小

	if (userAgent.indexOf('win') !== -1) {
		LODOP.SET_LICENSES('', 'AF8A5800B823915C50BCF67B925E8EA7', '', '');
		LODOP.ADD_PRINT_TEXT('7mm', '3mm', '65mm', '7mm', obj.locationCode);
	LODOP.SET_PRINT_STYLEA(0, 'FontSize', 11);
	LODOP.SET_PRINT_STYLEA(0, 'TextNeatRow', true);



	LODOP.ADD_PRINT_BARCODE('1.6mm', '70mm', '14.510mm', '14.510mm', 'QRCode', obj.locationBar);
	LODOP.SET_PRINT_STYLEA(0,"QRCodeVersion",2);
	LODOP.SET_PRINT_STYLEA(0, 'QRCodeErrorLevel', 'L');
	LODOP.PRINT();
	}
	if (userAgent.indexOf('linux') !== -1) {
		LODOP.SET_LICENSES('', 'E9663FC92B893B0D4F484B', '', '');
		LODOP.ADD_PRINT_TEXT('3mm', '3mm', '65mm', '7mm', obj.locationCode);
	LODOP.SET_PRINT_STYLEA(0, 'FontSize', 11);
	LODOP.SET_PRINT_STYLEA(0, 'TextNeatRow', true);



	LODOP.ADD_PRINT_BARCODE('0.1mm', '70mm', '12mm', '12mm', 'QRCode', obj.locationBar);
	LODOP.SET_PRINT_STYLEA(0,"QRCodeVersion",2);
	LODOP.SET_PRINT_STYLEA(0, 'QRCodeErrorLevel', 'L');
	LODOP.PRINT();
	}


};

//获取打印配置
let printMode = ref<any>('');
const getFormDetails = () => {
	getConfigurationObj().then((res: any) => {
		if (res.data) {
			printMode.value = res.data.printingEquipment;
		}
	});
};
onMounted(() => {
	getFormDetails();
	getWarehouseSelect();
});
</script>
