<template>
	<div>
		<el-row>
			<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
				<el-form-item prop="scenePlanName">
					<el-input v-model="state.queryForm.scenePlanName" placeholder="请输入场景预案名称" clearable class="!w-[500px]" />
				</el-form-item>
				<el-form-item>
					<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
				</el-form-item>
			</el-form>
			<el-table
				v-loading="state.loading"
				height="calc(100vh - 270px)"
				:data="leftDataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="场景预案名称" prop="scenePlanName" fixed="left" show-overflow-tooltip ></el-table-column>
				<el-table-column label="明细" prop="b" show-overflow-tooltip width="60">
					<template #default="scope">
						<el-popover effect="light" trigger="hover" placement="bottom" width="auto">
							<template #default>
								<div class="w-[600px] max-h-[300px]">
									<el-table
										:data="scope.row.planDetailListVO"
										stripe
										class="w-full"
										:max-height="300"
										border
										:cell-style="tableStyle.cellStyle"
										:header-cell-style="tableStyle.headerCellStyle"
									>
										<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip width="100"></el-table-column>
										<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip></el-table-column>
										<el-table-column label="数量" prop="materialNum" show-overflow-tooltip width="60"></el-table-column>
										<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip width="60"></el-table-column>
									</el-table>
								</div>
							</template>
							<template #reference>
								<el-button  icon="Tickets" text type="primary"> </el-button>
							</template>
						</el-popover>
					</template>
				</el-table-column>
				<el-table-column label="套数" show-overflow-tooltip width="150">
					<template #default="scope">
						<el-input-number v-model="scope.row.materialNum" :min="1" controls-position="right" />
					</template>
				</el-table-column>
				<el-table-column :label="$t('common.action')" fixed="right" width="60">
					<template #default="scope">
						<el-button  text type="primary" @click="addRightClick(scope.row)"> 添加 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</el-row>
	</div>
</template>

<script lang="ts" name="" setup>
import { pageList } from '/@/api/basicData/scenarioPlan';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();
// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		scenePlanName: '',
		status: 1,
	},
	pageList: pageList,
	dataList: [],
});
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

//左侧列表数据
let leftDataList = ref<any>([]);
const emit = defineEmits(['add-to-right']);
//将数据传递给父组件
const addRightClick = (row: any) => {
	if (!row.planDetailListVO?.length) return;
	let arr = row.planDetailListVO.map((item: any) => {
		return { ...item, materialNum: item.materialNum * row.materialNum };
	});
	emit('add-to-right', '', arr);
};
watch(
	() => state.dataList,
	(val) => {
		leftDataList.value = toRaw(state.dataList)?.map((item) => ({ ...item, materialNum: 1 }));
	},
	{ deep: true }
);
</script>
