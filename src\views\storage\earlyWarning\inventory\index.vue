<!-- 库存 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full">
					<div class="float-left">
						<el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList" ref="queryRef">
							<el-form-item label="物资名称" prop="logType">
								<el-input clearable placeholder="请输入物资名称" v-model="state.queryForm.materialName" class="!max-w-[180px]"></el-input>
							</el-form-item>
							<el-form-item label="库存区间" prop="createTime">
								<input-number-range :disabled="false" :precision="0" @input="changeFroms"></input-number-range>
							</el-form-item>
							<el-form-item>
								<el-button @click="getDataList" icon="Search" type="primary">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</el-row>

			<el-table
				ref="tableRef"
				:data="state.dataList"
				@sort-change="sortChangeHandle"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" />
				<el-table-column label="物资编码" prop="materialCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="仓库" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="当前库存" prop="inventoryNum" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
				<el-table-column label="库存预警值" prop="threshold" show-overflow-tooltip></el-table-column>
			</el-table>

			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination"></pagination>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import { pageList } from '/@/api/admin/log';
import { useI18n } from 'vue-i18n';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { stockPageList } from '/@/api/storage/earlyWarning';
import inputNumberRange from './inputNumberRange.vue';
const { t } = useI18n();

// 定义变量内容
const queryRef = ref();

let tableRef = ref(null);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		materialName: '',
		minInventoryNum: [],
		maxInventoryNum: '',
	},
	pageList: stockPageList,
});
//  table hook
const { downBlobFile, getDataList, currentChangeHandle: baseCurrentChangeHandle, sortChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// 分页事件
const currentChangeHandle = (page: number) => {
	// Reset table scroll position to top
	tableRef.value?.setScrollTop(0);
	// Call the original handler
	baseCurrentChangeHandle(page);
};
const changeFroms = (val: any) => {
	state.queryForm.minInventoryNum = val[0];
	state.queryForm.maxInventoryNum = val[1];
};
// onMounted 通过路由参数给  serviceId 赋值
const route = useRoute();
onMounted(() => {
	const { serviceId } = route.query;
	if (serviceId) {
		state.queryForm.serviceId = serviceId;
	}
	getDataList();
});
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
