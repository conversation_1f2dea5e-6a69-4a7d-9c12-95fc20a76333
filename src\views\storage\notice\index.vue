<!-- 通知记录 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full">
					<div class="float-left">
						<el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList" ref="queryRef">
							<el-form-item label="预警类型" prop="logType">
								<el-select placeholder="请选择预警类型" clearable v-model="state.queryForm.logType">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="时间" prop="createTime">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="-"
									@change="dateChange"
									type="daterange"
									v-model="state.queryForm.time"
									value-format="YYYY-MM-DD HH:mm:ss"
									format="YYYY-MM-DD"
								/>
							</el-form-item>
							<el-form-item>
								<el-button @click="getDataList" icon="Search" type="primary">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button @click="exportExcel" type="primary">导出</el-button>
					</div>
				</div>
			</el-row>

			<el-table
				ref="tableRef"
				:data="state.dataList"

				@sort-change="sortChangeHandle"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" />

				<el-table-column label="预警时间" prop="createTime" show-overflow-tooltip></el-table-column>
				<el-table-column label="预警类型"  show-overflow-tooltip>
					<template #default="scope">
				<span v-if="scope.row.warnType == '1'">库存预警</span>
				<span v-if="scope.row.warnType == '2'">过期预警</span>
				<span v-if="scope.row.warnType == '3'"> 未归还预警</span>

					</template>
				</el-table-column>
				<el-table-column label="预警通知人" prop="notifyUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="预警内容" prop="warnContent" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="预警方式" prop="notifyMethodList" show-overflow-tooltip></el-table-column>
			</el-table>

			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination"></pagination>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import {pageList } from '/@/api/storage/notice';
import { useI18n } from 'vue-i18n';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';

const LogDetailRef = ref();

// 定义查询字典
const { warnType } = useDict('warnType');
const { t } = useI18n();

// 定义变量内容
const queryRef = ref();


let tableRef = ref(null);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warnType: '',
		time: '',
		beginCreateTime: '',
		overCreateTime:''
	},
	pageList: pageList,
	createdIsNeed: true,
});

//  table hook
const { downBlobFile, getDataList, currentChangeHandle: baseCurrentChangeHandle, sortChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// 分页事件
const currentChangeHandle = (page: number) => {
	// Reset table scroll position to top
	tableRef.value?.setScrollTop(0);
	// Call the original handler
	baseCurrentChangeHandle(page);
};



// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/warnNotify/getExportWarnNotify', state.queryForm, '通知记录.xlsx');
};


const dateChange = (value: any) => {
	state.queryForm.beginCreateTime = '';
	state.queryForm.overCreateTime = '';
	if (!Array.isArray(value)) return;
	state.queryForm.beginCreateTime = value[0];
	state.queryForm.overCreateTime = value[1].replace(/\d{2}:\d{2}:\d{2}$/, '23:59:59');
};
// onMounted 通过路由参数给  serviceId 赋值
const route = useRoute();
onMounted(() => {
	const { serviceId } = route.query;
	if (serviceId) {
		state.queryForm.serviceId = serviceId;
	}
	getDataList();
});
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
