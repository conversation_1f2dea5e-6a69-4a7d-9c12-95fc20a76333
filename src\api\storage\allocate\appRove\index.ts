import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};

export const pageList = (obj?: Object) => {
	return request({
		url: '/admin/transferWarehouseBill/getApproveTransferWarehouseBillPage',
		method: 'post',
		data: obj,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};
//审批
export const addObj = (data?: Object) => {
	return request({
		url: '/admin/transferWarehouseBill/approveTransferWarehouseBill',
		method: 'post',
		headers: {
			'Content-Type': 'application/json',
		},
		data,
	});
};

//查看
export const getObj = (id?: any) => {
	return request({
		url: '/admin/transferWarehouseBill/getApproveTransferWarehouseBillById/' + id,
		method: 'get',
	});
};
