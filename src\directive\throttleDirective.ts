import type { App } from 'vue';

export function throttleDirective(app: App) {
	app.directive('debounce', {
		// 将指令名改为 debounce
		mounted(el, binding) {
			// 确保传入的绑定值是函数
			if (typeof binding.value !== 'function') {
				throw new Error('Callback must be a function');
			}
			// 获取自定义的防抖时间，如果没有传入，则默认为 300 毫秒
			const debounceTime = binding.arg ? Number(binding.arg) : 300;
			if (isNaN(debounceTime) || debounceTime <= 0) {
				throw new Error('Invalid debounce time');
			}
			let timer: ReturnType<typeof setTimeout> | null = null;
			// 定义点击事件的处理逻辑
			el.__handleClick__ = function () {
				if (timer) {
					clearTimeout(timer); // 清除之前的定时器
				}
				el.disabled = true;
				timer = setTimeout(() => {
					el.disabled = false;
					binding.value(); // 调用绑定的回调函数
				}, debounceTime); // 使用自定义的防抖时间
			};

			el.addEventListener('click', el.__handleClick__);
		},
		beforeUnmount(el) {
			if (el.__handleClick__) {
				el.removeEventListener('click', el.__handleClick__);
			}
		},
	});
}
