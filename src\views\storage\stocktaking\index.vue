<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="盘点类型" prop="checkType">
								<el-select placeholder="请选择盘点类型" clearable v-model="state.queryForm.checkType" class="!w-[160px]">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in [{value: 0, label: '按仓库位置'}, {value: 1, label: '按物资目录'}]" />
								</el-select>
							</el-form-item>
							<el-form-item label="仓库" prop="warehouseId">
								<el-select placeholder="请选择仓库" clearable v-model="state.queryForm.logTypewarehouseId" class="!w-[160px]">
									<el-option :key="item.value" :label="item.warehouseName" :value="item.id" v-for="item in warehouseData" />

								</el-select>
							</el-form-item>
							<el-form-item label="盘点人员" prop="checkUserId">
								<el-select placeholder="请选择盘点人员" clearable v-model="state.queryForm.checkUserId" class="!w-[160px]">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="盘点时间" prop="createTime">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="-"
									type="daterange"
									@change="dateChange"
									v-model="state.queryForm.time"
									value-format="YYYY-MM-DD HH:mm:ss"
									format="YYYY-MM-DD"
								/>
							</el-form-item>

							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button icon="folder-add" type="primary" @click="addClick">
							{{ $t('common.addBtn') }}
						</el-button>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="id"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="盘点类型"  show-overflow-tooltip>
					<template #default="scope">
						{{scope.row.checkType=='0'?'按仓库位置':scope.row.checkType=='1'?'按物资目录':''}}
					</template>
				</el-table-column>
				<el-table-column label="盘点区域" prop="warehouseLocationName" show-overflow-tooltip></el-table-column>
				<el-table-column label="状态"  show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.checkStatus=='0'">待盘点</span>
						<span v-else-if="scope.row.checkStatus=='1'">正在盘点</span>
						<span v-else-if="scope.row.checkStatus=='2'">已完成</span>
					</template>
				</el-table-column>
				<el-table-column label="盘点人员" prop="checkUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="盘点时间" prop="checkTime" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 查看  form-->
						<el-button text type="primary" @click="formClick(scope.row.id)"> 查看 </el-button>
						<!-- 删除 -->
						<el-button icon="delete" @click="handleDelete(scope.row.id)" text type="primary">{{ $t('common.delBtn') }} </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	<stockDialog @refresh="getDataList()" ref="stockDialogRef" />

	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { pageList, delObj} from '/@/api/storage/stocktaking';
import {  getWarehouse } from '/@/api/storage/warehouseManagement/purchase';

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';
// 引入组件
const stockDialog = defineAsyncComponent(() => import('./stockConfig.vue'));
// 定义查询字典
const { warnType } = useDict('warnType');
// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();


// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		time:'',
		checkType: '',
		warehouseId: '',
		checkUserId: '',
		beginCheckTime:'',
		endCheckTime: '',
		checkStatusList:''
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);



// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(id);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

//查看入库单页面
const formClick = (id?: any) => {
	router.push({
		path: '/storage/stocktaking/form',
		query: { id: id, notCreateTags: 'true' },
	});
};


const dateChange = (value: any) => {
	state.queryForm.beginCheckTime = '';
	state.queryForm.endCheckTime = '';
	if (!Array.isArray(value)) return;
	state.queryForm.beginCheckTime = value[0];
	state.queryForm.endCheckTime = value[1].replace(/\d{2}:\d{2}:\d{2}$/, '23:59:59');
};

//新增 修改页面
const router = useRouter();
let stockDialogRef=ref<any>()
const addClick = () => {
	stockDialogRef.value.openDialog()
};
// 获取仓库数据
const warehouseData = ref<any[]>([]);
const getWarehouseData = () => {
	getWarehouse().then((res: any) => {
		warehouseData.value = res.data;
	});
};
onMounted(() => {
	getWarehouseData();
});
</script>
