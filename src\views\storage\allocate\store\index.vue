<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="调拨申请单编号">
								<el-input v-model="state.queryForm.transferBillCode" placeholder="请输入调拨申请单编号" clearable style="max-width: 180px" />
							</el-form-item>
							<el-form-item label="入库单编号">
								<el-input v-model="state.queryForm.entryBillCode" placeholder="请输入出库单编号" clearable style="max-width: 180px" />
							</el-form-item>
							<el-form-item label="入库单状态">
								<el-select placeholder="请选择入库单状态" clearable v-model="state.queryForm.entryBillStatus">
									<el-option
										:key="item.value"
										:label="item.label"
										:value="item.value"
										v-for="item in [
											{ label: '待入库', value: 0 },
											{ label: '已入库', value: 1 },
											{ label: '已上架', value: 2 },
										]"
									/>
								</el-select>
							</el-form-item>

							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />

				<el-table-column label="调拨申请单编号" prop="transferBillCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库单编号" prop="entryBillCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库单状态" show-overflow-tooltip>
					<template #default="scope">
						{{ warehousingEntry[scope.row.billStatus] }}
					</template>
				</el-table-column>
				<el-table-column label="入库仓库" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库时间" prop="entryTime" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库部门" prop="entryDept" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库人员" prop="entryUser" show-overflow-tooltip></el-table-column>

				<el-table-column label="上架人员" prop="putShelfUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="上架时间" prop="putShelfTime" show-overflow-tooltip></el-table-column>

				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 核验入库 verification-->
						<el-button icon="" text type="primary" @click="confirmClick(scope.row.id)" v-if="scope.row.billStatus == '0'"> 核验入库 </el-button>

						<!-- 查看  form-->
						<el-button text type="primary" @click="formClick(scope.row.id)"> 查看 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { pageList } from '/@/api/storage/allocate/store';

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();


// 定义表格查询、后台调用的API
const warehousingEntry: any = {
	'0': '待入库',
	'1': '已入库',
	'2': '已上架',
};
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		transferBillCode: '',
		entryBillCode: '',
		entryBillStatus: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);




const router = useRouter();

//查看
const formClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/store/form',
		query: { id: id, notCreateTags: 'true' },
	});
};

//核验入库
const confirmClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/store/verification',
		query: { id: id, notCreateTags: 'true' },
	});
};


</script>
