export default {
	sysdict: {
		index: '#',
		importsysDictTip: 'import SysDict',
		id: 'id',
		dictType: 'dictType',
		description: 'description',
		createBy: 'createBy',
		updateBy: 'updateBy',
		createTime: 'createTime',
		updateTime: 'updateTime',
		remarks: 'remarks',
		systemFlag: 'systemFlag',
		inputDictTypeTip: 'input dictType',
		inputDescriptionTip: 'input description',
		inputCreateByTip: 'input createBy',
		inputUpdateByTip: 'input updateBy',
		inputCreateTimeTip: 'input createTime',
		inputUpdateTimeTip: 'input updateTime',
		inputRemarksTip: 'input remarks',
		inputSystemFlagTip: 'input systemFlag',
		inputDelFlagTip: 'input delFlag',
		inputTenantIdTip: 'input tenantId',
		dictItem: 'dict item',
		deleteDisabledTip: 'system data cannot be deleted ',
	},
	dictItem: {
		index: '#',
		name: 'dict item',
		importsysDictItemTip: 'import SysDictItem',
		id: 'id',
		dictId: 'dictId',
		itemValue: 'itemValue',
		label: 'label',
		dictType: 'dictType',
		description: 'description',
		sortOrder: 'sortOrder',
		createBy: 'createBy',
		updateBy: 'updateBy',
		createTime: 'createTime',
		updateTime: 'updateTime',
		remarks: 'remarks',
		delFlag: 'delFlag',
		tenantId: 'tenantId',
		inputIdTip: 'input id',
		inputDictIdTip: 'input dictId',
		inputItemValueTip: 'input itemValue',
		inputLabelTip: 'input label',
		inputDictTypeTip: 'input dictType',
		inputDescriptionTip: 'input description',
		inputSortOrderTip: 'input sortOrder',
		inputCreateByTip: 'input createBy',
		inputUpdateByTip: 'input updateBy',
		inputCreateTimeTip: 'input createTime',
		inputUpdateTimeTip: 'input updateTime',
		inputRemarksTip: 'input remarks',
		inputDelFlagTip: 'input delFlag',
		inputTenantIdTip: 'input tenantId',
	},
};
