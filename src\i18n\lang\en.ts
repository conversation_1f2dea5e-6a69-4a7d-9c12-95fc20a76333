export default {
	router: {
		home: 'Home',
		system: 'System',
		systemMenu: 'System Menu',
		systemRole: 'System Role',
		systemUser: 'System User',
		systemDept: 'System Department',
		systemDic: 'System Dictionary',
		limits: 'Permissions',
		limitsFrontEnd: 'Front End',
		limitsFrontEndPage: 'Front End Page',
		limitsFrontEndBtn: 'Front End Button',
		limitsBackEnd: 'Back End',
		limitsBackEndEndPage: 'Back End Page',
		menu: 'Menu',
		menu1: 'Menu 1',
		menu11: 'Menu 1-1',
		menu12: 'Menu 1-2',
		menu121: 'Menu 1-2-1',
		menu122: 'Menu 1-2-2',
		menu13: 'Menu 1-3',
		menu2: 'Menu 2',
		funIndex: 'Functions',
		funTagsView: 'Tags View',
		funCountup: 'Count Up',
		funWangEditor: 'Wang Editor',
		funCropper: 'Cropper',
		funQrcode: 'QR Code',
		funEchartsMap: 'Echarts Map',
		funPrintJs: 'PrintJS',
		funClipboard: 'Copy and Cut',
		funGridLayout: 'Drag Layout',
		funSplitpanes: 'Split Pane',
		funDragVerify: 'Validator',
		pagesIndex: 'Pages',
		pagesFiltering: 'Filtering',
		pagesFilteringDetails: 'Filtering Details',
		pagesFilteringDetails1: 'Filtering Details 1',
		pagesIocnfont: 'Icon Font Icon',
		pagesElement: 'Element Icon',
		pagesAwesome: 'Awesome Icon',
		pagesFormAdapt: 'Form Adapt',
		pagesTableRules: 'Table Rules',
		pagesFormI18n: 'Form I18n',
		pagesFormRules: 'Multi-Form Validation',
		pagesDynamicForm: 'Dynamic Complex Form',
		pagesWorkflow: 'Workflow',
		pagesListAdapt: 'List Adapt',
		pagesWaterfall: 'Waterfall',
		pagesSteps: 'Steps',
		pagesPreview: 'Large Preview',
		pagesWaves: 'Wave Effect',
		pagesTree: 'Tree Alter Table',
		pagesDrag: 'Drag Command',
		pagesLazyImg: 'Image Lazy Loading',
		makeIndex: 'Make Index',
		makeSelector: 'Icon Selector',
		makeNoticeBar: 'Notification Bar',
		makeSvgDemo: 'Svgicon Demo',
		makeTableDemo: 'Table Demo',
		paramsIndex: 'Routing Parameters',
		paramsCommon: 'General Routing',
		paramsDynamic: 'Dynamic Routing',
		paramsCommonDetails: 'General Routing Details',
		paramsDynamicDetails: 'Dynamic Routing Details',
		chartIndex: 'Chart Index',
		visualizingIndex: 'Visualizing Index',
		visualizingLinkDemo1: 'Visualizing Link Demo 1',
		visualizingLinkDemo2: 'Visualizing Link Demo 2',
		personal: 'Personal',
		tools: 'Tools',
		layoutLinkView: 'Link View',
		layoutIframeViewOne: 'Iframe View One',
		layoutIframeViewTwo: 'Iframe View Two',
	},
	staticRoutes: {
		login: 'Login',
		authredirect: 'Auth Redirect',
		expire: 'Password Expire',
		signIn: 'Sign In',
		notFound: 'Not Found',
		noPower: 'No Power',
	},
	user: {
		title0: 'Component Size',
		title1: 'Language Switching',
		title2: 'Menu Search',
		title3: 'Layout Configuration',
		title4: 'News',
		title5: 'Full Screen On',
		title6: 'Full Screen Off',
		dropdownLarge: 'Large',
		dropdownDefault: 'Default',
		dropdownSmall: 'Small',
		dropdown1: 'Home Page',
		dropdown2: 'Personal Center',
		dropdown5: 'Log Out',
		searchPlaceholder: 'Menu Search: Supports Chinese and Routing Path',
		newTitle: 'Notice',
		newBtn: 'All',
		newGo: 'Go to the Notification Center',
		newDesc: 'No Notice',
		logOutTitle: 'Tips',
		logOutMessage: 'This Operation Will Log Out. Do You Want to Continue?',
		logOutConfirm: 'Determine',
		logOutCancel: 'Cancel',
		logOutExit: 'Exiting',
	},
	tagsView: {
		refresh: 'Refresh',
		close: 'Close',
		closeOther: 'Close Other',
		closeAll: 'Close All',
		fullscreen: 'Fullscreen',
		closeFullscreen: 'Close Fullscreen',
		favorite: 'Favorite',
		favoriteMax: 'The number of shortcut navigation exceeds the upper limit. Please remove some shortcuts',
	},
	notFound: {
		foundTitle: 'Wrong Address Input, Please Re-enter the Address~',
		foundMsg: 'You Can Check the Web Address First, and Then Re-enter or Give Us Feedback.',
		foundBtn: 'Back to Home Page',
	},
	noAccess: {
		accessTitle: 'You Are Not Authorized to Operate~',
		accessMsg: 'Contact Information: Add QQ Group Discussion *********',
		accessBtn: 'Reauthorization',
	},
	layout: {
		configTitle: 'Layout Configuration',
		oneTitle: 'Global Themes',
		twoTopTitle: 'Top Bar Set Up',
		twoMenuTitle: 'Menu Set Up',
		twoColumnsTitle: 'Columns Set Up',
		twoTopBar: 'Top Bar Background',
		twoTopBarColor: 'Top Bar Default Font Color',
		twoIsTopBarColorGradual: 'Top Bar Gradient',
		twoMenuBar: 'Menu Background',
		twoMenuBarColor: 'Menu Default Font Color',
		twoMenuBarActiveColor: 'Menu Highlight Color',
		twoIsMenuBarColorGradual: 'Menu Gradient',
		twoColumnsMenuBar: 'Column Menu Background',
		twoColumnsMenuBarColor: 'Default Font Color Bar Menu',
		twoIsColumnsMenuBarColorGradual: 'Column Gradient',
		twoIsColumnsMenuHoverPreload: 'Column Menu Hover Preload',
		threeTitle: 'Interface Settings',
		threeIsCollapse: 'Menu Horizontal Collapse',
		threeIsUniqueOpened: 'Menu Accordion',
		threeIsFixedHeader: 'Fixed Header',
		threeIsClassicSplitMenu: 'Classic Layout Split Menu',
		threeIsLockScreen: 'Open the Lock Screen',
		threeLockScreenTime: 'Screen Locking (s/s)',
		fourTitle: 'Interface Display',
		fourIsShowLogo: 'Sidebar Logo',
		fourIsBreadcrumb: 'Open Breadcrumb',
		fourIsBreadcrumbIcon: 'Open Breadcrumb Icon',
		fourIsTagsview: 'Open Tagsview',
		fourIsTagsviewIcon: 'Open Tagsview Icon',
		fourIsCacheTagsView: 'Enable Tagsview Cache',
		fourIsSortableTagsView: 'Enable Tagsview Drag',
		fourIsShareTagsView: 'Enable Tagsview Sharing',
		fourIsFooter: 'Open Footer',
		fourIsGrayscale: 'Grey Model',
		fourIsInvert: 'Color Weak Mode',
		fourIsDark: 'Dark Mode',
		fourIsWartermark: 'Turn on Watermark',
		fourWartermarkText: 'Watermark Copy',
		fourIsChat: 'LLaMA Chat',
		fiveTitle: 'Other Settings',
		fiveTagsStyle: 'Tagsview Style',
		fiveAnimation: 'Page Animation',
		fiveColumnsAsideStyle: 'Column Style',
		fiveColumnsAsideLayout: 'Column Layout',
		sixTitle: 'Layout Switch',
		sixDefaults: 'One',
		sixClassic: 'Two',
		sixTransverse: 'Three',
		sixColumns: 'Four',
		tipText: 'Click the Button Below to Copy the Layout Configuration to `/src/stores/themeConfig.ts`. It Has Been Modified In.',
		copyText: 'Replication Configuration',
		resetText: 'Restore Default',
		copyTextSuccess: 'Copy Succeeded!',
		copyTextError: 'Copy Failed!',
	},
	upgrade: {
		title: 'New Version',
		msg: 'The New Version is Available, Please Update It Now! Dont Worry, the Update is Fast!',
		desc: 'Prompt: Update Will Restore the Default Configuration',
		btnOne: 'Cruel Refusal',
		btnTwo: 'Update Now',
		btnTwoLoading: 'Updating',
	},
};
