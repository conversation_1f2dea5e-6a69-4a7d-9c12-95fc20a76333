<template>
    <div>
      <div class="input-number-range" :class="{ 'is-disabled': disabled }">
        <div class="flex">
          <div class="from">
            <el-input
            class="!max-w-[180px]"
              ref="input_from"
              clearable
              v-model="userInputForm"
              :disabled="disabled"
              placeholder="请输入库存区间起始值"
              @blur="handleBlurFrom"
              @focus="handleFocusFrom"
              @input="handleInputFrom"
              @change="handleInputChangeFrom"
            ></el-input>
          </div>
          <div class="center">
            <span>至</span>
          </div>
          <div class="to">
            <el-input
              ref="input_to"
              v-model="userInputTo"
              :disabled="disabled"
              clearable
              class="!max-w-[180px]"

              placeholder="请输入库存区间结束值"
              @blur="handleBlurTo"
              @focus="handleFocusTo"
              @input="handleInputTo"
              @change="handleInputChangeTo"
            ></el-input>
          </div>
        </div>
      </div>
    </div>
  </template>

  <script setup>
  import { ref, watch, defineProps, defineEmits } from 'vue';

// 接收父组件传递的值
const props = defineProps({
    value: { required: true },
    disabled: {
        type: Boolean,
        default: false,
    },
    precision: {
        type: Number,
        default: 0,
        validator(val) {
            return val >= 0 && val === parseInt(val, 10);
        },
    },
});

// 用来发出事件的函数
// 添加所有要触发的事件名称
const emit = defineEmits([
    'changefrom',
    'changeto',
    'blurfrom',
    'focusfrom',
    'focusto',
    'blurto',
    'inputfrom',
    'inputto',
    'input'
]);

// 声明响应式变量
const userInputForm = ref(null);
const userInputTo = ref(null);

// 监听 value 的变化
watch(
    () => props.value,
    (value) => {
        if (value instanceof Array && props.precision !== undefined) {
            userInputForm.value = typeof value[0] === 'number' ? value[0] : null;
            userInputTo.value = typeof value[1] === 'number' ? value[1] : null;
        }
    },
    { immediate: true }
);

// 保留精度的处理函数
const toPrecision = (num, precision = 0) => {
    return parseFloat(Math.round(num * Math.pow(10, precision)) / Math.pow(10, precision));
};

// 各种事件处理
const handleBlurFrom = (event) => {
    emit('blurfrom', event);
};

const handleFocusFrom = (event) => {
    emit('focusfrom', event);
};

const handleBlurTo = (event) => {
    emit('blurto', event);
};

const handleFocusTo = (event) => {
    emit('focusto', event);
};

const handleInputFrom = (value) => {
    emit('inputfrom', value);
};

const handleInputTo = (value) => {
    emit('inputto', value);
};

const handleInputChangeFrom = (value) => {
    if (isNaN(value) || value === '') {
        emit('input', [null, userInputTo.value]);
        emit('changefrom', userInputForm.value);
        return;
    }

    userInputForm.value = setPrecisionValue(value);

    if (typeof userInputTo.value === 'number') {
        userInputForm.value = parseFloat(userInputForm.value) <= parseFloat(userInputTo.value)
          ? userInputForm.value
          : userInputTo.value;
    }
    emit('input', [userInputForm.value, userInputTo.value]);
    emit('changefrom', userInputForm.value);
};

const handleInputChangeTo = (value) => {
    console.log("🚀 ~ handleInputChangeTo ~ value:", value)
    if (isNaN(value) || value === '') {
        emit('input', [userInputForm.value, null]);
        // 修正此处事件名，应该是 'changeto'
        emit('changeto', userInputTo.value);
        return;
    }

    userInputTo.value = setPrecisionValue(value);

    if (typeof userInputForm.value === 'number') {
        userInputTo.value =
            parseFloat(userInputTo.value) >= parseFloat(userInputForm.value)
              ? userInputTo.value
              : userInputForm.value;
    }
    emit('input', [userInputForm.value, userInputTo.value]);
    emit('changeto', userInputTo.value);
};

// 设置精度的函数
const setPrecisionValue = (value) => {
    if (props.precision !== undefined) {
        return toPrecision(value, props.precision);
    }
    return null;
};
  </script>

  <style lang="scss" scoped>
  ::v-deep .el-input--mini .el-input__inner  {
    border: 0px !important;
  margin: 0 !important;
  padding: 0 15px !important;
  background-color: transparent !important;
  }

  .flex {
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: center;
    align-items: center;
    .center {
      margin-top: 1px;
    }
  }
  .is-disabled {
    background-color: #eef0f6;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
  }
  </style>
