// 分区管理

import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};
//区域下拉
export const getArea = (id: string) => {
	return request({
		url: '/admin/warehouseZone/selectWarehouseZoneList/' + id,
		method: 'get',
	});
};
export const pageList = (params?: Object) => {
	return request({
		url: '/admin/warehouseZone/getWarehouseZonePage',
		method: 'get',
		params,
	});
};

export function addObj(obj?: Object) {
	return request({
		url: '/admin/warehouseZone/addWarehouseZone',
		method: 'post',
		data: obj,
	});
}

export function getObj(id?: string) {
	return request({
		url: '/admin/warehouseZone/getWarehouseZoneById/' + id,
		method: 'get',
	});
}

export function delObj(id?: string) {
	return request({
		url: '/admin/warehouseZone/deleteWarehouseZoneById/' + id,
		method: 'get',
	});
}
