import request from '/@/utils/request';

export const deptTree = (params?: Object) => {
	return request({
		url: '/admin/materialCategory/getMaterialCategoryTree',
		method: 'get',
		params,
	});
};

export const addObj = (obj: Object) => {
	return request({
		url: '/admin/materialCategory/addMaterialCategory',
		method: 'post',
		data: obj,
	});
};

export const getObj = (id: string) => {
	return request({
		url: '/admin/materialCategory/getMaterialCategoryById/' + id,
		method: 'get',
	});
};

export const delObj = (id: string) => {
	return request({
		url: '/admin/materialCategory/deleteMaterialCategoryById/' + id,
		method: 'get',
	});
};

export const putObj = (obj: Object) => {
	return request({
		url: '/admin/materialCategory/updateMaterialCategory',
		method: 'post',
		data: obj,
	});
};

// export const syncUser = () => {
// 	return request({
// 		url: '/admin/connect/sync/ding/user',
// 		method: 'post',
// 	});
// };

// export const syncDept = () => {
// 	return request({
// 		url: '/admin/connect/sync/ding/dept',
// 		method: 'post',
// 	});
// };

// export const syncCpUser = () => {
// 	return request({
// 		url: '/admin/connect/sync/cp/user',
// 		method: 'post',
// 	});
// };

// export const syncCpDept = () => {
// 	return request({
// 		url: '/admin/connect/sync/cp/dept',
// 		method: 'post',
// 	});
// };

// export const orgTree = (type: String, deptId: Number) => {
// 	return request({
// 		url: '/admin/dept/org',
// 		method: 'get',
// 		params: { type: type, parentDeptId: deptId },
// 	});
// };

// export const orgTreeSearcheUser = (param: Object) => {
// 	return request({
// 		url: '/admin/dept/org/user/search',
// 		method: 'get',
// 		params: param,
// 	});
// };
