import request from '/@/utils/request';
//过期预警
export const expiredPageList = (data?: Object) => {
	return request({
		url: '/admin/expirationWarnings/getExpirationWarningsPage',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};
//库存预警
export const stockPageList = (data?: Object) => {
	return request({
		url: '/admin/inventoryAlerts/getInventoryAlertsPage',
		method: 'get',
		params: data,
	});
};
