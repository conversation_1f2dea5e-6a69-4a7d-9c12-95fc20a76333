<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-tabs v-model="activeName" @tab-click="handleClick">
				<el-tab-pane lazy label="库存预警" name="inventory">
					<template #label>
						<div class="ml-8">库存预警</div>
					</template>
					<inventory-config />
				</el-tab-pane>
				<el-tab-pane lazy label="过期预警" name="expire">
					<template #label> 过期预警 </template>
					<expire-config />
				</el-tab-pane>
				<el-tab-pane lazy label="未归还预警" name="noReturn">
					<template #label> 未归还预警 </template>
					<no-return-config />
				</el-tab-pane>
				<el-tab-pane lazy label="库存预警值设置" name="warningValueSetting">
					<template #label> 库存预警值设置 </template>
					<warning-value-setting />
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>

<script setup lang="ts" name="systemSysMessage">
// 引入组件
import { TabsPaneContext } from 'element-plus';
//库存预警
const inventoryConfig = defineAsyncComponent(() => import('./inventory/index.vue'));
//过期预警
const expireConfig = defineAsyncComponent(() => import('./expire/index.vue'));
//未归还预警
const noReturnConfig = defineAsyncComponent(() => import('./noReturn/index.vue'));
//库存预警值设置
const warningValueSetting = defineAsyncComponent(() => import('./warningValueSetting/index.vue'));

const activeName = ref('inventory');

const handleClick = (tab: TabsPaneContext, event: Event) => {};
</script>
