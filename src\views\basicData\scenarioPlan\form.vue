<template>
	<div class="system-user-dialog-container">
		<el-dialog :close-on-click-modal="false" draggable v-model="visible" height="600" width="1200">
			<template #header>
				<el-row>
					<div>
						<span class="mr-3"> 场景预案名称： </span>
						<span> {{ form.name }}</span>
					</div>
				</el-row>
				<el-row class="mb-2 mt-2">
					<div>
						<span class="ml-7 mr-3"> 启用状态： </span>
						<span> {{ form.status == '0' ? '禁用' : '启用' }}</span>
					</div>
				</el-row>
				<el-row>
					<div>
						<span class="mr-3"> 使用场景描述： </span>
						<span> {{ form.desc }}</span>
					</div>
				</el-row>
			</template>
			<el-row class="mb4">
				<span> 物资清单 </span>
			</el-row>
			<el-table :data="tableData" border :cell-style="tableStyle.cellStyle" :header-cell-style="tableStyle.headerCellStyle">
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="materialCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip></el-table-column>
				<el-table-column label="数量" prop="materialNum" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
			</el-table>
		</el-dialog>
	</div>
</template>

<script lang="ts" name="systemUserDialog" setup>
import { useI18n } from 'vue-i18n';
import { BasicTableProps, useTable } from '/@/hooks/table';

const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);

const { t } = useI18n();

// 定义变量内容
const visible = ref(false);
let form = reactive({
	name: '',
	status: '',
	desc: '',
});
let tableData = ref<any[]>();

// 打开弹窗
const openDialog = async (name: string, status: string, desc: string, table: any[]) => {
	Object.assign(form, {
		name,
		status,
		desc,
	});
	tableData.value = table;
	visible.value = true;
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
