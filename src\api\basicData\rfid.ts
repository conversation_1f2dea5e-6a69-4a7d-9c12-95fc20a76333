import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};

export const pageList = () => {
	return request({
		url: '/admin/rfidDevice/getRfidDeviceList',
		method: 'get',
	});
};
export const addObj = (data?: Object) => {
	return request({
		url: '/admin/rfidDevice/addRfidDevice',
		method: 'post',
		data,
	});
};
export const putObj = (data?: Object) => {
	return request({
		url: '/admin/rfidDevice/updateRfidDevice',
		method: 'post',
		data,
	});
};

export const delObj = (id?: string) => {
	return request({
		url: '/admin/rfidDevice/deleteRfidDeviceById/' + id,
		method: 'get',
	});
};

export const getObj = (id?: string) => {
	return request({
		url: '/admin/rfidDevice/getRouterById/' + id,
		method: 'get',
	});
};
