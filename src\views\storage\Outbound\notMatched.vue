<template>
	<div class="system-user-dialog-container">
		<el-dialog :close-on-click-modal="false" draggable v-model="visible" height="200" width="400">
			<template #header>
				<el-row>
					<div>
						<span> 未匹配条码： </span>
						<span> {{ tableData.length}}</span>
					</div>
				</el-row>
			</template>
            <div class="!max-h-[200px] mb20">
                <div v-for="(item, index) in tableData" >
                <div>
                  {{ item }}
                </div>
            </div>
            </div>


		</el-dialog>
	</div>
</template>

<script lang="ts" name="systemUserDialog" setup>
import { useI18n } from 'vue-i18n';
import { BasicTableProps, useTable } from '/@/hooks/table';

const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);

const { t } = useI18n();

// 定义变量内容
const visible = ref(false);
let tableData = ref<any[]>([]);

// 打开弹窗
const openDialog = async ( barList: any) => {
	tableData.value = barList;
	visible.value = true;
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
