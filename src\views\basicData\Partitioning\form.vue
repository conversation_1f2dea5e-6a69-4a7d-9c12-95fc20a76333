Codebox 
<template>
	<el-dialog :close-on-click-modal="false" :title="false ? $t('common.editBtn') : $t('common.addBtn')" draggable v-model="visible">
		<el-form :model="form" :rules="dataRules" formDialogRef label-width="105px" ref="dataFormRef" >
			<el-row :gutter="20">
				<el-col :span="12" class="mb20">
					<el-form-item label="所属仓库" prop="warehouseId">
						<el-select placeholder="请选择所属仓库" v-model="form.warehouseId">
							<el-option :key="index" :label="item.warehouseName" :value="item.id" v-for="(item, index) in warehouseData"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="货架类型" prop="shelfType">
						<el-select placeholder="请选择货架类型" v-model="form.shelfType">
							<el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in shelf_type"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="区号" prop="zoneCode">
						<el-input placeholder="请输入区号" v-model="form.zoneCode" />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="区名称" prop="zoneName">
						<el-input placeholder="请输入区名称" v-model="form.zoneName" />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="列数（S）" prop="shelfNum">
						<el-input placeholder="请输入列数（S）" v-model="form.shelfNum" />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="启用面（F）" prop="faceStatus">
						<el-select placeholder="请选择启用面（F）" v-model="form.faceStatus">
							<el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in enable_status"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="组数（G）" prop="groupNum">
						<el-input placeholder="请输入组数（G）" v-model="form.groupNum" />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="层数（L）" prop="levelNum">
						<el-input placeholder="请输入层数（L）" v-model="form.levelNum" />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="货位数（C）" prop="cellNum">
						<el-input placeholder="请输入货位数（C" v-model="form.cellNum" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20" v-if="form.shelfType == '3'">
				<el-col :span="12" class="mb20">
					<el-form-item label="编号排序" prop="orderType">
						<el-select placeholder="请选择编号排序" v-model="form.orderType">
							<el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in order_type"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="主控列编号" prop="mainControlColumnCode">
						<el-input placeholder="请输入主控列编号" v-model="form.mainControlColumnCode" />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="IP" prop="ip">
						<el-input placeholder="请输入IP" v-model="form.ip" />
					</el-form-item>
				</el-col>
				<el-col :span="12" class="mb20">
					<el-form-item label="端口号" prop="port">
						<el-input placeholder="请输入端口号" v-model="form.port" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
				<el-button @click="onSubmit" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script lang="ts" name="SysPublicParamDialog" setup>
// 定义子组件向父组件传值/事件
import { useDict } from '/@/hooks/dict';
import { useMessage } from '/@/hooks/message';
import { getWarehouse, addObj } from '/@/api/basicData/Partitioning';
import { useI18n } from 'vue-i18n';
import { rule } from '/@/utils/validate';

const emit = defineEmits(['refresh']);

const { t } = useI18n();

// 定义变量内容
const warehouseData = ref<any[]>([]);
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 定义字典
const { order_type, enable_status, shelf_type } = useDict('order_type', 'enable_status', 'shelf_type');

// 提交表单数据
const form = reactive({
	warehouseId: '',
	shelfType: '',
	zoneCode: '',
	zoneName: '',
	shelfNum: '',
	faceStatus: '0',
	groupNum: '',
	levelNum: '',
	cellNum: '',
	orderType: '',
	mainControlColumnCode: '',
	ip: '',
	port: '',
});

// 定义校验规则
const dataRules = reactive({
	warehouseId: [{ required: true, message: '所属仓库不能为空', trigger: 'change' }],
	shelfType: [{ required: true, message: '货架类型不能为空', trigger: 'change' }],
	zoneCode: [
		{ validator: rule.number, trigger: 'change' },
		{ required: true, message: '区号不能为空', trigger: 'change' },
	],
	zoneName: [{ required: true, message: '区名称不能为空', trigger: 'change' }],
	shelfNum: [
		{ validator: rule.number, trigger: 'change' },

		{ required: true, message: '列数（S）不能为空', trigger: 'change' },
	],
	faceStatus: [{ required: true, message: '启用面（F）不能为空', trigger: 'change' }],

	cellNum: [
		{ validator: rule.number, trigger: 'change' },
		{ required: true, message: '货位数（C）不能为空', trigger: 'change' },
	],
	orderType: [{ required: true, message: '编号排序不能为空', trigger: 'change' }],
	//主控列编号数值不能大于列数（S）
	mainControlColumnCode: [
		{ validator: rule.number, trigger: 'change' },
		{ validator: (rule: any, value: any, callback: any) => {
			if (value && form.shelfNum && parseInt(value) > parseInt(form.shelfNum)) {
				callback(new Error('主控列编号不能大于列数（S）'));
			} else {
				callback();
			}
		}, trigger: 'change' },
		{ required: true, message: '主控列编号不能为空', trigger: 'change' }
	],
	ip: [{ required: true, message: 'IP不能为空', trigger: 'change' }],
	port: [{ required: true, message: '端口号不能为空', trigger: 'change' }],
});

// 打开弹窗
const openDialog = () => {
	visible.value = true;
	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});
	//获取所需数据
	getWarehouseData();
};

// 获取仓库数据
const getWarehouseData = () => {
	getWarehouse().then((res: any) => {
		warehouseData.value = res.data;
	});
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
		let { ip, port, orderType, mainControlColumnCode, ...data } = form;
		form.shelfType == '3' ? await addObj(form) : await addObj(data);
		useMessage().success(t('common.addSuccessText'));
		visible.value = false; // 关闭弹窗
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
