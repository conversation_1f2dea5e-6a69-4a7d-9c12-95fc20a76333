<!-- 拣选管理 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-tabs v-model="activeName" @tab-click="handleClick">
				<el-tab-pane label="基站管理" name="baseStation">
					<template #label>
						<div class="ml-8">基站管理</div>
					</template>
					<base-config />
				</el-tab-pane>
				<el-tab-pane label="电子标签管理" name="label">
					<template #label> 电子标签管理 </template>
					<label-config />
				</el-tab-pane>
				<el-tab-pane label="巷道灯管理" name="lamp">
					<template #label> 巷道灯管理 </template>
					<lamp-config />
				</el-tab-pane>
				<el-tab-pane label="参数管理" name="parameter">
					<template #label> 参数管理 </template>
					<parameter-config />
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>

<script setup lang="ts" name="systemSysMessage">
// 引入组件
import { TabsPaneContext } from 'element-plus';
import {	refreshTags,refreshTheAlleyLights } from '/@/api/basicData/selectionManagement/label';

// 基站管理  电子标签管理  巷道灯管理  参数管理   baseStation   label   lamp  parameter
const BaseConfig = defineAsyncComponent(() => import('./baseStation/index.vue'));
const LabelConfig = defineAsyncComponent(() => import('./label/index.vue'));
const LampConfig = defineAsyncComponent(() => import('./lamp/index.vue'));
const ParameterConfig = defineAsyncComponent(() => import('./parameter/index.vue'));

const activeName = ref('baseStation');

const handleClick = async(tab: TabsPaneContext, event: Event) => {
	console.log(tab.props.label);
	if (tab.props.label == '巷道灯管理') {
		await refreshData('3')
	} else if (tab.props.label == '电子标签管理') {
		await refreshData('2')
	}
};

const refreshData = async (type: string) => {
	type=='3'?await refreshTheAlleyLights(): await refreshTags()
};
</script>
