<template>
	<el-dialog :title="form.id ? $t('common.editBtn') : $t('common.addBtn')" v-model="visible" :close-on-click-modal="false" draggable width="600">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="100px">
			<el-form-item label="所属仓库" prop="warehouseId">
				<el-select v-model="form.warehouseId" placeholder="请选择所属仓库">
					<el-option v-for="item in warehouseData" :key="item.value" :label="item.warehouseName" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="基站IP" prop="routerIp">
				<el-input v-model="form.routerIp" placeholder="请输入基站IP" />
			</el-form-item>
			<el-form-item label="基站ID" prop="routerIdentity">
				<el-input v-model="form.routerIdentity" placeholder="请输入基站ID" />
			</el-form-item>
			<el-form-item label="模板ID" prop="styleId">
				<el-input v-model="form.styleId" placeholder="请输入模板ID" />
			</el-form-item>
			<el-form-item label="映射号" prop="mappingType">
				<el-input v-model="form.mappingType" placeholder="请输入映射号" />
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" v-debounce="onSubmit">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { useMessage } from '/@/hooks/message';
import { addObj, getObj, putObj, getWarehouse } from '/@/api/basicData/selectionManagement/baseStation';
import { useI18n } from 'vue-i18n';
import { rule } from '/@/utils/validate';

const { t } = useI18n();

const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 提交表单数据
const form = reactive({
	id: '',
	warehouseId: '',
	routerIp: '',
	routerIdentity: '',
	mappingType: '',
	styleId: '',
});

// 定义校验规则
const dataRules = ref({
	warehouseId: [{ required: true, message: '所属仓库不能为空', trigger: 'change' }],
	routerIp: [{ required: true, message: '基站IP不能为空', trigger: 'change' }],
	routerIdentity: [
	{ validator: rule.number, trigger: 'change' },

		{ required: true, message: '基站ID不能为空', trigger: 'change' }
	],
	mappingType: [
	{ validator: rule.number, trigger: 'change' },
	{ required: true, message: '映射号不能为空', trigger: 'change' }],
	styleId: [
	{ validator: rule.number, trigger: 'change' },
	{ required: true, message: '模板ID不能为空', trigger: 'change' }],
});

// 打开弹窗
const openDialog = (id: string) => {
	visible.value = true;
	form.id = '';
	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});
	if (id) {
		form.id = id;
		getConfigData(id);
	}
	//获取所需数据
	getWarehouseData();
};
// 获取仓库数据
let warehouseData = ref<any[]>([]);
const getWarehouseData = () => {
	getWarehouse().then((res: any) => {
		warehouseData.value = res.data;
	});
};
// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		let { id, ...other } = form;
		form.id ? await putObj(form) : await addObj(other);
		useMessage().success(form.id ? t('common.editSuccessText') : t('common.addSuccessText'));

		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

// 初始化表单数据
const getConfigData = (id: string) => {
	// 获取数据
	loading.value = true;
	getObj(id)
		.then((res: any) => {
			Object.assign(form, res.data);
		})
		.finally(() => {
			loading.value = false;
		});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
