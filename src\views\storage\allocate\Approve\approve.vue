<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row class="mt20 mb-2">
				<el-col :span="24">
					<Descriptions title="" :column="4" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>

						<DescriptionsItem label="调拨申请单编号">{{ detailsData?.billCode }}</DescriptionsItem>
						<DescriptionsItem label="调拨状态">{{
							detailsData?.billStatus == 0
								? '待审批'
								: detailsData?.billStatus == 1
								? '待出库'
								: detailsData?.billStatus == 2
								? '驳回'
								: detailsData?.billStatus == 3
								? '待接收'
								: detailsData?.billStatus == 4
								? '完成'
								: ''
						}}</DescriptionsItem>
						<DescriptionsItem label="调出仓库">{{ detailsData?.outWarehouse }}</DescriptionsItem>
						<DescriptionsItem label="接收仓库">{{ detailsData?.entryWarehouse }}</DescriptionsItem>
						<DescriptionsItem label="调拨申请部门">{{ detailsData?.applyDept }}</DescriptionsItem>
						<DescriptionsItem label="调拨申请人">{{ detailsData?.applyUser }}</DescriptionsItem>
						<DescriptionsItem label="申请时间">{{ detailsData?.applyTime }}</DescriptionsItem>
						<DescriptionsItem label="调拨申请原因">{{ detailsData?.applyReason }}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="detailsData?.billDetailList"
				height="calc(100vh - 310px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				ref="tableRefs"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="materialCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
				<el-table-column label="我的库存" prop="inventoryNum" show-overflow-tooltip></el-table-column>
			</el-table>

			<div style="position: relative;top:20px">

				<el-form ref="dataFormRef" :inline="true" :model="form" :rules="formRules">
					<el-form-item label="审批结果" prop="approveStatus">
						<el-select placeholder="请选择审批结果" clearable v-model="form.approveStatus">
							<el-option
								:key="item.value"
								:label="item.label"
								:value="item.value"
								v-for="item in [
									{ label: '同意', value: 1 },
									{ label: '驳回', value: 2 },
								]"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="审批意见" prop="approveOpinion">
						<el-input  type="textarea" :autosize="{ minRows: 2, maxRows: 3 }" v-model="form.approveOpinion" placeholder="请输入审批意见" clearable class="!w-[280px]" />
					</el-form-item>
				</el-form>
			</div>
			<el-row class="fixed bottom-[15px] right-[20px]">
				<el-button @click="returnClick">取消</el-button>
				<el-button type="primary" @click="confirmClick" :disabled="loading">确认</el-button>
			</el-row>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { addObj, getObj } from '/@/api/storage/allocate/appRove';

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 动态引入组件

const { t } = useI18n();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);
const dataFormRef = ref();
const loading = ref(false);

const form = ref({
	approveStatus: '',
	approveOpinion: '',
});
const formRules = ref({
	approveStatus: [{ required: true, message: '审批结果不能为空', trigger: 'change' }],
	approveOpinion: [{ required: true, message: '审批意见不能为空', trigger: 'change' }],
});
const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/allocate/Approve/index' });
};

const route = useRoute();
const detailsData = ref<any>();
onMounted(async () => {
	await getObj(route.query?.id).then((res) => {
		detailsData.value = res.data;
	});
});

// 提交
const confirmClick = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {

		loading.value = true;
		await addObj({
			...form.value,
			id: route.query?.id,
		});
		useMessage().success('审批成功');
		returnClick()
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
