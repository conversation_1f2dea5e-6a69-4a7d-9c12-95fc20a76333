import type { App } from 'vue';
import { authDirective } from '/@/directive/authDirective';
import { wavesDirective } from '/@/directive/customDirective';
import { throttleDirective } from '/@/directive/throttleDirective';

/**
 * 导出指令方法：v-xxx
 * @methods authDirective 用户权限指令，用法：v-auth
 * @methods wavesDirective 按钮波浪指令，用法：v-waves
 * @methods throttleDirective 防抖指令，用法：v-throttle
 */
export function directive(app: App) {
	// 用户权限指令
	authDirective(app);
	// 按钮波浪指令
	wavesDirective(app);
	// 防抖指令
	throttleDirective(app);
	// focus
	app.directive('focus', {
		mounted(el) {
			el.focus();
		},
	});
}
