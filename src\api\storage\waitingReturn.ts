import request from '/@/utils/request';
//分页查询
export const pageList = (data?: Object) => {
	return request({
		url: '/admin/outWarehouseBill/getPendingInquiryPage',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};

//物资名称 联想
export const getMaterialName = (params?: Object) => {
	return request({
		url: '/admin/materialCatalog/getMaterialName',
		method: 'get',
		params,
	});
};
//部门
export const getDeptTree = () => {
	return request({
		url: '/admin/outWarehouseBill/getByDept',
		method: 'post',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};
//人员下拉传部门id
export const getPeople = (params?: Object) => {
	return request({
		url: '/admin/user/page',
		method: 'get',
		params,
	});
};
export const getObj = (billCode: any) => {
	return request({
		url: ' /admin/outWarehouseBill/selectOutboundInformation/' + billCode,
		method: 'post',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};
export const getDetailsObj = (materialCode: string) => {
	return request({
		url: '/admin/outWarehouseBill/getEncodingQueryDetailsPage/' + materialCode,
		method: 'post',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

export const urgeReturn = (billCode: string) => {
	return request({
		url: ' /admin/outWarehouseBill/callback/' + billCode,

		method: 'post',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};
