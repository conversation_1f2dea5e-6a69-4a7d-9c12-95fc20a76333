<template>
	<div>
		<el-row>
			<div class="mb8 w-full">
				<div class="float-right">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" > 新 增 </el-button>
				</div>
			</div>
		</el-row>
		<el-table
			:data="state.dataList"
			v-loading="state.loading"
			border
			max-height="620px"

			:cell-style="tableStyle.cellStyle"
			:header-cell-style="tableStyle.headerCellStyle"
		>
			<el-table-column type="index" label="序号" width="60" />
			<el-table-column prop="warehouseName" label="所属仓库" show-overflow-tooltip />
			<el-table-column prop="routerIp" label="基站IP" show-overflow-tooltip />
			<el-table-column prop="routerIdentity" label="基站ID" show-overflow-tooltip>

			</el-table-column>
			<el-table-column  label="状态" show-overflow-tooltip >
			<template #default="scope">
				{{ scope.row.state? '在线' : '离线' }}
				</template>
			</el-table-column>
			<el-table-column prop="styleId" label="模板ID" show-overflow-tooltip />
			<el-table-column prop="mappingType" label="映射号" show-overflow-tooltip />

			<el-table-column label="操作" width="300">
				<template #default="scope">
					<el-button icon="edit-pen" text type="primary"  @click="formDialogRef.openDialog(scope.row.id)">修改 </el-button>
					<el-button icon="delete" text type="primary"  @click="handleDelete(scope.row.id)">删除 </el-button>
				</template>
			</el-table-column>
		</el-table>
		<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemSmsMessage">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { delObj, pageList } from '/@/api/basicData/selectionManagement/baseStation';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义变量内容
const formDialogRef = ref();

const state: BasicTableProps = reactive<BasicTableProps>({
	pageList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));

	} catch {
		return;
	}

	try {
		await delObj(id);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
</script>
