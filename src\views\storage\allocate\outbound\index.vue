<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
						<el-form-item label="调拨申请单编号">
							<el-input v-model="state.queryForm.transferBillCode" placeholder="请输入调拨申请单编号" clearable style="max-width: 180px" />
						</el-form-item>
						<el-form-item label="出库单编号">
							<el-input v-model="state.queryForm.billCode" placeholder="请输入出库单编号" clearable style="max-width: 180px" />
						</el-form-item>
						<el-form-item label="出库单状态">
							<el-select placeholder="请选择出库单状态" clearable v-model="state.queryForm.billStatus">
								<el-option
									:key="item.value"
									:label="item.label"
									:value="item.value"
									v-for="item in [
										{ label: '待确认', value: 0 },
										{ label: '待出库', value: 1 },
										{ label: '已出库', value: 2 },
									]"
								/>
							</el-select>
						</el-form-item>
						<el-form-item label="申请部门" prop="deptId">
							<el-tree-select
								:data="deptData"
								:props="{ value: 'id', label: 'name', children: 'children' }"
								check-strictly
								class="w200"
								clearable
								placeholder="请选择申请部门"
								@change="handleDepartmentChange"
								v-model="state.queryForm.applyDeptId"
							>
							</el-tree-select>
						</el-form-item>
						<el-form-item label="申请人员" prop="warehouseAdminIdList">
							<el-select placeholder="请选择申请人员" v-model="state.queryForm.applyUserId" >
								<el-option :key="index" :label="item.name" :value="item.userId" v-for="(item, index) in peopleData"></el-option>
							</el-select>
						</el-form-item>

						<el-form-item>
							<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
						</el-form-item>
					</el-form>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="调拨申请单编号" prop="transferBillCode" show-overflow-tooltip></el-table-column>

				<el-table-column label="出库单编号" prop="billCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="出库单状态"  show-overflow-tooltip>
					<template #default="scope">
						{{ billStatus[scope.row.billStatus] }}
					</template>
				</el-table-column>
				<el-table-column label="出库仓库" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请部门" prop="applyDept" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请人员" prop="applyUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="出库人员" prop="outUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="出库时间" prop="outTime" show-overflow-tooltip></el-table-column>


				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 拣货 picking -->
						<el-button text type="primary" @click="pickingClick(scope.row.id)" v-if="scope.row.billStatus == '1'"> 拣货 </el-button>
						<!-- 出库  outbound-->
						<el-button text type="primary" @click="outboundClick(scope.row.id)" v-if="scope.row.billStatus == '1'"> 出库 </el-button>
						<!-- 查看  form-->
						<el-button text type="primary" @click="formClick(scope.row.id)" v-if="scope.row.billStatus == '2'"> 查看 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { pageList } from '/@/api/storage/allocate/outbound';
import { getPeople } from '/@/api/storage/outbound';
import { deptTree } from '/@/api/admin/dept';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';


const { t } = useI18n();

// 定义变量内容
const queryRef = ref();


// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		transferBillCode: '',
		billCode: '',
		billStatus: '',
		applyDeptId: '',
		applyUserId: "",
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);



const router = useRouter();

//查看入库单页面
const formClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/outbound/form',
		query: { id: id, notCreateTags: 'true' },
	});
};
//出库
const outboundClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/outbound/outbound',
		query: { id: id, notCreateTags: 'true' },
	});
};
//拣货
const pickingClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/outbound/picking',
		query: { id: id, notCreateTags: 'true' },
	});
};

const deptData = ref<any>([]);
const peopleData = ref<any>([]);
// 部门变更时清空管理人员
const handleDepartmentChange = async (id: any) => {
	if (!id) {
		state.queryForm.applyDeptId = '';
		peopleData.value = [];

		return;
	} else {
		await getPeopleData(id);
	}
};
const billStatus = ref<any>({
	'0': '待确认',
	'1': '待出库',
	'2': '已出库',
});
// 初始化部门数据
const getDeptData = () => {
	deptTree().then((res: any) => {
		deptData.value = res.data;
	});
};
// 获取部门下人员
const getPeopleData = async (id: any) => {
	const { records } = (await getPeople({ deptId: id })).data;
	peopleData.value = records;
};

onMounted(() => {
	getDeptData();
});
</script>
