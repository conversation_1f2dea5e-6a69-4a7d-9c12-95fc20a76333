<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full" shadow="hover">
					<div class="float-left mr20">
						<el-form :inline="true" :model="state.queryForm" ref="queryRef">
							<el-form-item label="所属仓库" class="ml2" prop="">
								<el-select placeholder="请选择所属仓库" v-model="state.queryForm.warehouseId" clearable @change="getZoneCodeSelect">
									<el-option :key="index" :label="item.warehouseName" :value="item.id" v-for="(item, index) in warehouseData"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="区号" class="ml2" prop="zoneCode">
								<el-select placeholder="请选择所属区号" v-model="state.queryForm.zoneCode" clearable>
									<el-option :key="index" :label="item.zoneCode" :value="item.zoneCode" v-for="(item, index) in zoneCodeData"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="区名称" prop="zoneName">
								<el-input placeholder="请输入区名称" style="max-width: 180px" v-model="state.queryForm.zoneName" clearable />
							</el-form-item>
							<el-form-item label="货架类型" class="ml2" prop="shelfType">
								<el-select placeholder="请选择货架类型" v-model="state.queryForm.shelfType" clearable>
									<el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in shelf_type"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item>
								<el-button @click="getDataList" formDialogRef icon="search" type="primary">
									{{ $t('common.queryBtn') }}
								</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div style="float: right">
						<el-button  @click="formDialogRef.openDialog()" class="ml10" icon="folder-add" type="primary">
							{{ $t('common.addBtn') }}
						</el-button>
					</div>
				</div>
			</el-row>
			<el-table
				:data="state.dataList"
				style="width: 100%"
				row-key="id"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				ref="tableRefs"
			>
				<el-table-column :label="$t('partitioning.serialNumber')" type="index" width="60" />
				<el-table-column :label="$t('partitioning.warehouse')" prop="warehouseName" show-overflow-tooltip />
				<el-table-column :label="$t('partitioning.zoneNumber')" prop="zoneCode" show-overflow-tooltip />
				<el-table-column :label="$t('partitioning.zoneName')" prop="zoneName" show-overflow-tooltip />
				<el-table-column :label="$t('partitioning.shelfType')" prop="shelfType" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="shelf_type" :value="scope.row.shelfType"></dict-tag>
					</template>
				</el-table-column>
				<el-table-column :label="$t('partitioning.columnCount')" prop="shelfNum" show-overflow-tooltip />
				<el-table-column :label="$t('partitioning.enableFace')" show-overflow-tooltip>
					<template #default="scope">
						{{ faceStatusMap[scope.row.faceStatus] || '' }}
					</template>
				</el-table-column>
				<el-table-column :label="$t('partitioning.groupCount')" prop="groupNum" show-overflow-tooltip />
				<el-table-column :label="$t('partitioning.layerCount')" prop="levelNum" show-overflow-tooltip />
				<el-table-column :label="$t('partitioning.locationCount')" prop="cellNum" show-overflow-tooltip />
				<el-table-column :label="$t('common.action')" width="150">
					<template #default="scope">
						<el-button icon="delete"  @click="handleDelete(scope.row.id)" text type="primary">
							{{ $t('common.delBtn') }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog @refresh="getDataList()" ref="formDialogRef" />
	</div>
</template>

<script lang="ts" name="systemSysPublicParam" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import { delObj, pageList, getWarehouse,getArea } from '/@/api/basicData/Partitioning';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import { useI18n } from 'vue-i18n';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
const { t } = useI18n();
// 定义查询字典
const { shelf_type } = useDict('shelf_type');
// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();

const faceStatusMap: any = {
	'1': '启用',
	'0': '禁用',
};
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: '',
		zoneCode: '',
		zoneName: '',
		shelfType: '',
	},
	pageList: pageList,
	// descs: ['create_time'],
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(id);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
let warehouseData = ref<any[]>([]);
let zoneCodeData = ref<any[]>([]);
const getWarehouseSelect = async () => {
	warehouseData.value = (await getWarehouse())?.data || [];
};
const getZoneCodeSelect = async (id: string) => {
		state.queryForm.zoneCode = '';
	zoneCodeData.value = (await getArea(id))?.data || [];
};

onMounted(() => {
	getWarehouseSelect();
});
</script>
