<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full flex justify-between items-center">
					<div class="flex items-center cursor-pointer" @click="returnClick">
						<img src="/@/assets/back.png" class="w-5 h-5 mr-1" />
						<div class="text-14" style="color: #2e5cf6">返回</div>
					</div>
					<div>
						<el-button type="primary" @click="">定位</el-button>
						<el-button type="primary" @click="">复位</el-button>

						<el-button @click="exportPdf">导出</el-button>
					</div>
				</div>
			</el-row>
			<el-row class="mt20 mb-2">
				<el-col :span="24">
					<Descriptions title="上架指引" :column="5" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>
						<DescriptionsItem label="入库单编号">{{ detailsData?.billCode }}</DescriptionsItem>
						<DescriptionsItem label="入库仓库">{{ detailsData?.warehouseName }}</DescriptionsItem>
						<DescriptionsItem label="入库单状态">{{ detailsData?.billStatusName }}</DescriptionsItem>

						<DescriptionsItem label="入库部门">{{ detailsData?.entryDept }}</DescriptionsItem>
						<DescriptionsItem label="入库人员">{{ detailsData?.entryUser }}</DescriptionsItem>
						<DescriptionsItem label="入库时间">{{ detailsData?.entryTime }}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>

			<el-row>
				<div class="mb-2 font-bold w-full">物资清单</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="detailsData?.billDetailList"
				height="calc(100vh - 400px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				ref="tableRefs"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
				<el-table-column label="推荐位置" show-overflow-tooltip width="260">
					<template #header>
						<div>
							<span>推荐位置</span>
							<el-tooltip class="box-item" effect="light" content="全部收起" placement="top-start">
								<el-button v-if="tableData.some((row:any) => needsExpansion(row.barCode))" link type="primary" size="small" @click="toggleAllExpand">
									<img :src="zdImg" v-show="isAllExpanded" />
								</el-button>
							</el-tooltip>
						</div>
					</template>
					<template #default="scope">
						<div v-if="scope.row.barCode">
							<div class="flex justify-items-start">
								<div>
									<div v-for="(code, index) in getDisplayCodes(scope.row)" :key="index">
										{{ code }}
									</div>
								</div>
								<el-button v-if="needsExpansion(scope.row.barCode)" link type="primary" size="small" @click="toggleExpand(scope.row, scope.$index)">
									<img :src="scope.row.isExpanded ? zdImg : zkImg" :alt="scope.row.isExpanded ? '收起' : '展开'" />
								</el-button>
							</div>
						</div>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { getObj } from '/@/api/storage/warehouseManagement/other';

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 动态引入组件

const { t } = useI18n();

// 多选rows
// 是否可以多选

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});
const { downBlobFile, tableStyle } = useTable(state);

const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/warehouseManagement/other/index' });
};

// 表格内展开折叠
import zkImg from '/@/assets/flod.png';
import zdImg from '/@/assets/open.png';
const MAX_DISPLAY_LINES = 2;
//table ref
const tableRefs = ref();
//表格数据
const tableData = ref<any>([]);
//全部数据，截取最大行数   截取字段为row.barCode
const getDisplayCodes = (row: any) => {
	const barCode = row.barCode;
	if (!barCode) return [];
	const codes = barCode.split(',');
	if (codes.length > MAX_DISPLAY_LINES && !row.isExpanded) {
		return [...codes.slice(0, MAX_DISPLAY_LINES - 1)];
	}
	return codes;
};
//table滚动到指定行
const scrollToRow = (tableRef: any, rowIndex: number) => {
	nextTick(() => {
		if (tableRef.value) {
			// @ts-ignore
			const tableBody = tableRef.value.$el.querySelector('.el-table__body-wrapper');
			if (tableBody) {
				const row = tableBody.querySelectorAll('.el-table__row')[rowIndex - 1];
				if (row) {
					row.scrollIntoView({ behavior: 'smooth' });
				}
			}
		}
	});
};
//将所有行 收起
const toggleAllExpand = () => {
	tableData.value.forEach((row: any) => {
		row.isExpanded = false;
	});
};
//有展开的行  则展示表头收起图标
const isAllExpanded = computed(() => tableData.value.some((row: any) => row.isExpanded));
//判断长度需要展开字段的数据长度
const needsExpansion = (barCode?: string) => {
	return barCode ? barCode.split(',').length > MAX_DISPLAY_LINES : false;
};
//行内展开收起事件
const toggleExpand = (row: any, rowIndex: any) => {
	row.isExpanded = !row.isExpanded;
	if (!row.isExpanded) scrollToRow(tableRefs, rowIndex + 1);
};

const route = useRoute();
const detailsData = ref<any>();
onMounted(async () => {
	await getObj(route.query?.id).then((res) => {
		detailsData.value = res.data;
		detailsData.value.billDetailList.forEach((item: any) => {
			(item.barCode = item.materialList && item.materialList.join(',')), (item.isExpanded = false);
		});
	});
});
const exportPdf = async () => {
	// let res= await downPdf(route.query?.id)
	// downBlobFile(`/admin/entryWarehouseBill/pdfEntryWarehouseBillById/${route.query?.id}`,'', '入库单.pdf');
};
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
