<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<div class="text-[15px] font-bold">今日出入库统计</div>
			<div class="flex mt-[37px] ml-[40px]">
				<div class="flex items-center w-[293px]">
					<img src="/@/assets/abnormalOutbound/img1.png" width="68px" height="68px" />
					<div class="flex flex-col items-center ml-[24px]">
						<div class="text-[26px] font-[PingFang SC]">666</div>
						<div class="text-[#4E5969] text-[14px]">入库人次</div>
					</div>
				</div>
				<el-divider direction="vertical" class="h-[68px]" />
				<div class="flex items-center w-[293px] ml-[42px]">
					<img src="/@/assets/abnormalOutbound/img2.png" width="68px" height="68px" />
					<div class="flex flex-col items-center ml-[24px]">
						<div class="text-[26px] font-[PingFang SC]">666</div>
						<div class="text-[#4E5969] text-[14px]">入库标签</div>
					</div>
				</div>
				<el-divider direction="vertical" class="h-[68px]" />
				<div class="flex items-center w-[293px] ml-[42px]">
					<img src="/@/assets/abnormalOutbound/img3.png" width="68px" height="68px" />
					<div class="flex flex-col items-center ml-[24px]">
						<div class="text-[26px] font-[PingFang SC]">666</div>
						<div class="text-[#4E5969] text-[14px]">出库人次</div>
					</div>
				</div>
				<el-divider direction="vertical" class="h-[68px]" />
				<div class="flex items-center w-[293px] ml-[42px]">
					<img src="/@/assets/abnormalOutbound/img4.png" width="68px" height="68px" />
					<div class="flex flex-col items-center ml-[24px]">
						<div class="text-[26px] font-[PingFang SC]">666</div>
						<div class="text-[#4E5969] text-[14px]">出库标签</div>
					</div>
				</div>

				<el-divider direction="vertical" class="h-[68px]" />
				<div class="flex items-center w-[293px] ml-[42px]">
					<img src="/@/assets/abnormalOutbound/img5.png" width="68px" height="68px" />
					<div class="flex flex-col items-center ml-[24px]">
						<div class="text-[26px] font-[PingFang SC]">666</div>
						<div class="text-[#4E5969] text-[14px]">报警次数</div>
					</div>
				</div>
			</div>
			<el-divider />
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="通道门" prop="logType">
								<el-select placeholder="请选择通道门" clearable v-model="state.queryForm.warehouseId" class="!w-[160px]">
									<el-option :key="item.id" :label="item.warehouseName" :value="item.id" v-for="item in warehouseData" />
								</el-select>
							</el-form-item>
							<el-form-item label="物资条码">
								<el-input v-model="state.queryForm.billCode" placeholder="请输入物资条码" clearable class="!max-w-[180px]" />
							</el-form-item>
							<el-form-item label="时间区间" prop="time">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="-"
									type="datetimerange"
									@change="dateChange"
									v-model="state.queryForm.time"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</el-form-item>

							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button icon="folder-add" type="primary" @click="exportClick"> 导出 </el-button>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="通道门名称" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资编码" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资条码" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="出库时间" prop="warehouseName" show-overflow-tooltip></el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { allocationPageList, getWarehouse } from '/@/api/storage/statistics';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: '',
		beginEndEffectiveTime: '',
		overEndEffectiveTime: '',
		time: '',
	},
	pageList: allocationPageList,
	createdIsNeed: false,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);
const dateChange = (value: any) => {
	state.queryForm.beginEndEffectiveTime = '';
	state.queryForm.overEndEffectiveTime = '';
	if (!Array.isArray(value)) return;
	state.queryForm.beginEndEffectiveTime = value[0];
	state.queryForm.overEndEffectiveTime = value[1];
};

const exportClick = () => {
	downBlobFile('/admin/allocationStatistics/getExportAllocationStatistics', state.queryForm, '调拨统计.xlsx');
};
let warehouseData = ref<any>([]);

const getWarehouseData = async () => {
	const res = await getWarehouse();
	warehouseData.value = res.data;
};

onMounted(() => {
	getWarehouseData();
});
</script>
