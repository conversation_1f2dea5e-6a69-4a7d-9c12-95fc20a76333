import request from '/@/utils/request';

export const materialCategoryTree = (params:any) => {
	return request({
		url: '/admin/materialCategory/getMaterialCategoryTree',
		method: 'get',
		params
	});
};
export const pageList = (params?: Object) => {
	return request({
		url: '/admin/materialCatalog/getMaterialCatalogPage',
		method: 'get',
		params,
	});
};

export const switchRow = (obj: Object) => {
	return request({
		url: '/admin/materialCatalog/updateMaterialCatalogStatus',
		method: 'post',
		data: obj,
	});
};
//新增
export const addObj = (obj: Object) => {
	return request({
		url: '/admin/materialCatalog/addMaterialCatalog',
		method: 'post',
		data: obj,
	});
};
//详情
export const getObj = (id: String) => {
	return request({
		url: '/admin/materialCatalog/getMaterialCatalogById/' + id,
		method: 'get',
	});
};

export const putObj = (obj: Object) => {
	return request({
		url: '/admin/materialCatalog/updateMaterialCatalog',
		method: 'post',
		data: obj,
	});
};
