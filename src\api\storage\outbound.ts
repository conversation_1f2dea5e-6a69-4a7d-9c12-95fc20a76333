// 分区管理

import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};
//区域下拉
export const getArea = (id: string) => {
	return request({
		url: '/admin/warehouseZone/selectWarehouseZoneList/' + id,
		method: 'get',
	});
};
//物资名称 联想
export const getMaterialName = (params?: Object) => {
	return request({
		url: '/admin/materialCatalog/getMaterialName',
		method: 'get',
		params,
	});
};
//分页出库单列表
export const pageList = (data?: Object) => {
	return request({
		url: '/admin/outWarehouseBill/common/getCommonOutWarehouseBillPage',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

//人员下拉传部门id
export const getPeople = (params?: Object) => {
	return request({
		url: '/admin/user/page',
		method: 'get',
		params,
	});
};
//根据出库单id获取出库单详情
export const getObj = (id: any) => {
	return request({
		url: ' /admin/outWarehouseBill/common/getOutWarehouseBillById/' + id,
		method: 'get',
	});
};
//删除
export const delObj = (id?: string) => {
	return request({
		url: '/admin/outWarehouseBill/common/deleteOutWarehouseBillById/' + id,
		method: 'get',
	});
};
//新增出库单
export const addObj = (data?: Object) => {
	return request({
		url: '/admin/outWarehouseBill/common/addOutWarehouseBill',
		method: 'post',
		data,
	});
};
//修改出库单
export const putObj = (data?: Object) => {
	return request({
		url: '/admin/outWarehouseBill/common/updateOutWarehouseBill',
		method: 'post',
		data,
	});
};
//根据出库单id获取拣货详情
export const getPickingObj = (id: any) => {
	return request({
		url: ' /admin/outWarehouseBill/common/getOutWarehouseBillPickingById/' + id,
		method: 'get',
	});
};

//确认
export const confirmObj = (id: any) => {
	return request({
		url: ' /admin/outWarehouseBill/common/confirmOutWarehouseBillById/' + id,
		method: 'get',
	});
};
//根据出库单id获取出库单详情
export const getOutboundObj = (id: any) => {
	return request({
		url: ' /admin/outWarehouseBill/common/getOutWarehouseBillById/' + id,
		method: 'get',
	});
};
//出库-物资条码匹配
export const filterTags = (data?: Object) => {
	return request({
		url: '/admin/outWarehouseBill/common/outMaterialBarMatching',
		method: 'post',
		data,
	});
};
//出库提交
export const submitObj = (data?: Object) => {
	return request({
		url: '/admin/outWarehouseBill/common/submitOutWarehouseBill',
		method: 'post',
		data,
	});
};

//定位亮灯
export const positioning = (data?: Object) => {
	return request({
		url: '/admin/outWarehouseBill/common/locateLight',
		method: 'post',
		data,
	});
};
//灭灯
export const lighting = (data?: Object) => {
	return request({
		url: '/admin/outWarehouseBill/common/locateLightOff',
		method: 'post',
		data,
	});
};
