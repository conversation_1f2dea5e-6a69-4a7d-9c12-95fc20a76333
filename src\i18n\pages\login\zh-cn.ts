// 定义内容
export default {
	label: {
		one1: '用户名登录',
		two2: '手机号登录',
		three3: '社交登录',
		register: '注册账号',
	},
	link: {
		one3: '第三方登录',
		two4: '友情链接',
	},
	password: {
		serialNumberLabel: '序列号',
		registrationNoLabel: '注册码',
		registrationNo: '请输入注册码',
		accountPlaceholder1: '请输入用户名',
		accountPlaceholder2: '请输入密码',
		accountPlaceholder3: '请输入验证码',
		phonePlaceholder4: '请输入手机号',
		accountBtnText: '登 录',
		registerBtnText: '注 册',
		resetBtnText: '重 置',
		readAccept: '我已仔细阅读并接受',
		privacyPolicy: '《隐私政策》',
		oldPassword: '请输入原密码',
		newPassword: '请输入新密码',
		confirmPassword: '请确认新密码',
		backToLogin: '返回登录',
		mobileLogin: '验证码登录',
		createAccount: '注册账号',
		imageCodeTip: '请输入验证码',
		rememberPassword: '记住密码',
		registration: '软件注册',
	},
	mobile: {
		registration: '软件注册',

		placeholder1: '请输入手机号',
		placeholder2: '请输入验证码',
		codeText: '获取验证码',
		btnText: '登 录',
		mobileLogin: '验证码登录',
		rememberPassword: '记住密码',
		backToLogin: '返回登录',
		createAccount: '注册账号',
		sendSuccess: '验证码发送功',
		seconds: '秒后重发',
		mobileRequired: '请输入手机号码',
		codeRequired: '请输入验证码',
		codeLength: '验证码必须是4位数字',
		sendFailed: '发送验证码失败',
		loginSuccess: '登录成功',
		loginFailed: '登录失败',
		signIn: '登 录',
	},
	scan: {
		text: '打开手机扫一扫，快速登录/注册',
		wechatApp: '微信扫码体验移动端',
		appErrorTip: '应用未配置，登录失败',
	},
	signInText: '欢迎回来！',
	browserMsgText: '* 温馨提示：建议使用谷歌、Microsoft Edge，版本 80 及以上浏览器，360浏览器请使用极速模式',
	expire: {
		oldPassword: '请输入原密码',
		newPassword: '请输入新密码',
		confirmPassword: '请确认新密码',
		passwordRule: '两次输入的密码不一致',
		passwordScore: '密码强度太低',
		resetSuccess: '密码重置成功',
	},
	tenantSelect: {
		select: '选择租户',
		loadError: '获取租户列表失败',
	},
	register: {
		usernameEmpty: '用户名不能为空',
		usernameLength: '用户名称长度必须介于 5 和 20 之间',
		phoneEmpty: '手机号不能为空',
		registrationNo: '注册码不能为空',
		passwordEmpty: '密码不能为空',
		passwordLength: '用户密码长度必须介于 6 和 20 之间',
		passwordStrength: '密码强度太低',
		termsRequired: '请阅读并同意条款',
	},
	divider: {
		or: '或',
	},
	socialLogin: {
		wechatWork: '企微',
		dingtalk: '钉钉',
	},
};
