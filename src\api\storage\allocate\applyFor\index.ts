import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};

//获取调出仓库
export const getRieveWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectAllWarehouseList',
		method: 'get',
	});
};

export const pageList = (obj?: Object) => {
	return request({
		url: '/admin/transferWarehouseBill/getApplyTransferWarehouseBillPage',
		method: 'post',
		data: obj,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};
//新增
export const addObj = (data?: Object) => {
	return request({
		url: '/admin/transferWarehouseBill/addTransferWarehouseBill',
		method: 'post',
		headers: {
			'Content-Type': 'application/json',
		},
		data,
	});
};
//修改
export const putObj = (data?: Object) => {
	return request({
		url: '/admin/transferWarehouseBill/updateTransferWarehouseBill',
		method: 'post',
		data,
	});
};
//删除
export const delObj = (id?: string) => {
	return request({
		url: '/admin/transferWarehouseBill/deleteTransferWarehouseBillById/' + id,
		method: 'get',
	});
};

//查看  详情
export const getObj = (id?: any) => {
	return request({
		url: '/admin/transferWarehouseBill/getTransferWarehouseBillById/' + id,
		method: 'get',
	});
};
