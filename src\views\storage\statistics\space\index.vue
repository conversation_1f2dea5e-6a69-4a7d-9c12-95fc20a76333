
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full" >
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">

							<el-form-item label="所属仓库" prop="warehouseId">
							<el-select
								placeholder="请选择所属仓库"
								v-model="state.queryForm.warehouseId"
								style="max-width: 180px"
								clearable
								@change="getZoneCodeSelect"
							>
								<el-option :key="index" :label="item.warehouseName" :value="item.id" v-for="(item, index) in warehouseData"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="区号" prop="zoneCode">
							<el-select
								placeholder="请选择区号"
								v-model="state.queryForm.warehouseZoneId"
								style="max-width: 180px"
								clearable
								@change="getColumnSelect"
							>
								<el-option :key="index" :label="item.zoneCode" :value="item.id" v-for="(item, index) in zoneCodeData"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="列号" prop="shelfCode">
							<el-select placeholder="请选择列号 " v-model="state.queryForm.shelfCode" style="max-width: 180px" clearable>
								<el-option :key="index" :label="item.shelfCode" :value="item.shelfCode" v-for="(item, index) in columnCodeData"></el-option>
							</el-select>
						</el-form-item>

							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>

				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="仓库" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="区名称" prop="zoneName" show-overflow-tooltip></el-table-column>
				<el-table-column label="列号" prop="shelfCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="已用货位数" prop="onShelfCellCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="总货位数" prop="allCellNum" show-overflow-tooltip></el-table-column>
				<el-table-column label="空间利用率" prop="spaceUtilization" show-overflow-tooltip></el-table-column>

			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { suPageList } from '/@/api/storage/statistics';

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';
import { getArea, getColumn, getWarehouse } from '/@/api/basicData/locationManagement/label';
// 定义查询字典
const { warnType } = useDict('warnType');
// 动态引入组件

const { t } = useI18n();


const queryRef = ref();



// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: '',
		warehouseZoneId: '',
		shelfCode: '',
	},
	pageList: suPageList,
	createdIsNeed: false,

});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);


let warehouseData = ref<any[]>([]);
let zoneCodeData = ref<any[]>([]);
let columnCodeData = ref<any[]>([]);
const getWarehouseSelect = async () => {
	warehouseData.value = (await getWarehouse())?.data || [];
	// if(warehouseData.value.length){
	// 	state.queryForm.warehouseId = warehouseData.value[0].id;
	// 	getZoneCodeSelect(warehouseData.value[0].id);
	// }
};
const getZoneCodeSelect = async (id: string) => {
	state.queryForm.warehouseZoneId = '';
	state.queryForm.shelfCode = '';
	if (!id) return;
	zoneCodeData.value = (await getArea(id))?.data || [];
};
const getColumnSelect = async (id: string) => {
	state.queryForm.shelfCode = '';
	if (!id) return;
	columnCodeData.value = (await getColumn(id))?.data || [];
};

onMounted(async () => {
	await getWarehouseSelect();
});
</script>
