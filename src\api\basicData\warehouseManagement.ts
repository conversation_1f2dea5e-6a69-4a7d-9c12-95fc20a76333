/** 仓库管理*/
import request from '/@/utils/request';
export const pageList = (data?: Object) => {
	return request({
		url: '/admin/warehouse/getWarehousePage',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};
//人员下拉传部门id
export const getPeople = (params?: Object) => {
	return request({
		url: '/admin/user/page',
		method: 'get',
		params,
	});
};

export function addObj(obj?: Object) {
	return request({
		url: '/admin/warehouse/addWarehouse',
		method: 'post',
		data: obj,
	});
}

export function getObj(id?: string) {
	return request({
		url: '/admin/warehouse/getWarehouseById/' + id,
		method: 'get',
	});
}

export function delObj(id: string) {
	return request({
		url: '/admin/warehouse/deleteWarehouseById/' + id,
		method: 'get',
	});
}

export function putObj(obj?: Object) {
	return request({
		url: '/admin/warehouse/updateWarehouse',
		method: 'post',
		data: obj,
	});
}
