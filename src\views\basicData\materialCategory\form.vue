<template>
	<el-dialog :title="dataForm.parentCategoryIdList ? $t('common.editBtn') : $t('common.addBtn')" v-model="visible" width="600">
		<el-form ref="deptDialogFormRef" :model="dataForm" label-width="90px" :rules="dataRules" >
			<el-form-item label="上级类别" prop="parentCategoryId" v-if="!dataForm.parentCategoryIdList">
				<el-tree-select
					v-model="dataForm.parentCategoryId"
					:data="parentData"
					:props="{ value: 'id', label: 'name', children: 'children' }"
					class="w100"
					clearable
					check-strictly
					:render-after-expand="false"
					placeholder="请选择上级类别"
				/>
			</el-form-item>
			<el-form-item label="类别名称" prop="categoryName">
				<el-input v-model="dataForm.categoryName" placeholder="请输入类别名称" clearable />
			</el-form-item>
			<el-form-item label="排序" prop="categoryOrder">
				<el-input-number v-model="dataForm.categoryOrder" :placeholder="$t('sysdept.inputsortOrderTip')" clearable />
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="systemDeptDialog">
import { useI18n } from 'vue-i18n';
import { getObj, deptTree, addObj, putObj } from '/@/api/basicData/materialCategory';
import { useMessage } from '/@/hooks/message';
import { rule } from '/@/utils/validate';

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
const { t } = useI18n();
// 定义变量内容
const deptDialogFormRef = ref();
const dataForm = reactive({
	parentCategoryId: '',
	categoryName: '',
	categoryOrder: 9999,
	parentCategoryIdList: '',
});
const parentData = ref<any[]>([]);
const visible = ref(false);
const loading = ref(false);

const dataRules = ref({
	parentCategoryId: [{ required: true, message: '上级类别不能为空', trigger: 'change' }],
	categoryName: [
		{ validator: rule.overLength, trigger: 'change' },
		{ required: true, message: '类别名称不能为空', trigger: 'change' },
	],
	categoryOrder: [{ required: true, message: '排序不能为空', trigger: 'change' }],
});

// 打开弹窗
const openDialog = (type: string, id: string, praentId?: string) => {
	visible.value = true;
	dataForm.parentCategoryIdList = '';

	nextTick(() => {
		deptDialogFormRef.value?.resetFields();
		dataForm.parentCategoryId = id;
	});

	if (type === 'edit') {
		getObj(id)
			.then((res) => {
				Object.assign(dataForm, res.data, { parentCategoryId: id });
			})
			.catch((err) => {
				useMessage().error(err.msg);
			});
	}

	getDeptData();
};

// 提交
const onSubmit = async () => {
	const valid = await deptDialogFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;
		dataForm.parentCategoryIdList
			? await putObj({ id: dataForm.parentCategoryId, categoryName: dataForm.categoryName, categoryOrder: dataForm.categoryOrder })
			: await addObj({ parentCategoryId: dataForm.parentCategoryId, categoryName: dataForm.categoryName, categoryOrder: dataForm.categoryOrder });
		useMessage().success(t(dataForm.parentCategoryIdList ? 'common.editSuccessText' : 'common.addSuccessText'));
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 从后端获取菜单信息
const getDeptData = async () => {
	deptTree({showRoot:'1'}).then((res) => {
		parentData.value = [];
		parentData.value=res.data
	});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
