<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="出库单编号" prop="phone">
								<el-input v-model="state.queryForm.billCode" placeholder="请输入出库单编号" clearable class="!max-w-[180px]" />
							</el-form-item>
							<el-form-item label="申请部门" prop="deptId">
								<el-tree-select
									:data="deptData"
									:props="{ value: 'id', label: 'name', children: 'children' }"
									check-strictly
									class="w200"
									clearable
									placeholder="请选择申请部门"
									@change="handleDepartmentChange"
									v-model="state.queryForm.applyDeptId"
								>
								</el-tree-select>
							</el-form-item>
							<el-form-item label="申请人员" prop="warehouseAdminIdList">
								<el-select placeholder="请选择申请人员" v-model="state.queryForm.applyUserId" multiple clearable>
									<el-option :key="index" :label="item.name" :value="item.userId" v-for="(item, index) in peopleData"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="预计归还时间" prop="createTime">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="-"
									type="daterange"
									@change="dateChange"
									v-model="state.queryForm.time"
									value-format="YYYY-MM-DD HH:mm:ss"
									format="YYYY-MM-DD"
								/>
							</el-form-item>
							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="出库单编号" prop="billCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="出库用途" prop="outPurpose" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请部门" prop="nameDept" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请人员" prop="applyUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="出库时间" prop="outTime" show-overflow-tooltip></el-table-column>
				<el-table-column label="预计归还时间" prop="planReturnTime" show-overflow-tooltip></el-table-column>
				<el-table-column label="超期天数" prop="overdueSky" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 查看  form-->
						<el-button text type="primary" @click="formClick(scope.row.billCode)"> 查看 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { pageList, getPeople, getDeptTree } from '/@/api/storage/waitingReturn';
import { deptTree } from '/@/api/admin/dept';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();
const deptData = ref<any[]>([]);
const peopleData = ref<any[]>([]);

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		billCode: '',
		applyDeptId: '',
		applyUserId: '',
		beginCreateTime: '',
		overCreateTime: '',
		time: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);
const dateChange = (value: any) => {
	state.queryForm.beginCreateTime = '';
	state.queryForm.overCreateTime = '';
	if (!Array.isArray(value)) return;
	state.queryForm.beginCreateTime = value[0];
	state.queryForm.overCreateTime = value[1].replace(/\d{2}:\d{2}:\d{2}$/, '23:59:59');
};
// 部门变更时清空管理人员
const handleDepartmentChange = async (id: any) => {
	if (!id) {
		state.queryForm.applyDeptId = '';
		peopleData.value = [];

		return;
	} else {
		await getPeopleData(id);
	}
};

// 初始化部门数据
const getDeptData = () => {
	deptTree().then((res: any) => {
		deptData.value = res.data;
	});
};
// 获取部门下人员
const getPeopleData = async (id: any) => {
	const { records } = (await getPeople({ deptId: id })).data;
	peopleData.value = records;
};

onMounted(() => {
	getDeptData();
});

const router = useRouter();

//查看入库单页面
const formClick = (id?: any) => {
	router.push({
		path: '/storage/waitingReturn/form',
		query: { id: id, notCreateTags: 'true' },
	});
};
</script>
