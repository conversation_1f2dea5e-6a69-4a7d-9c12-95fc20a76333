// 分区管理

import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',

	});
};
export const pageList = (data?: Object) => {
	
	return request({
		url: '/admin/router/getRouterPage',
		method: 'post',
		data,
	});
};

export function addObj(obj?: Object) {
	return request({
		url: '/admin/router/addRouter',
		method: 'post',
		data: obj,
	});
}

export function getObj(id?: string) {
	return request({
		url: '/admin/router/getRouterById/' + id,
		method: 'get',
	});
}

export const putObj = (data?: Object) => {
	return request({
		url: '/admin/router/updateRouter',
		method: 'post',
		data,
	});
};
export function delObj(id?: string) {
	return request({
		url: '/admin/router/deleteRouterById/' + id,
		method: 'get',
	});
}
