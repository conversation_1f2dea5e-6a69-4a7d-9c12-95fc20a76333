<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-right">
						<el-button icon="folder-add" type="primary" @click="backupsClick"> 备份 </el-button>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="文件名称" show-overflow-tooltip>
					<template #default="scope">
						{{ scope.row }}
					</template>
				</el-table-column>
				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<popover-input v-model="inputPassword" @confirm="changePassword(scope.row)">
							<template #default>
								<el-button v-auth="'sys_user_edit'" icon="RefreshLeft" text type="primary" class="mr-4"> 还原 </el-button>
							</template>
						</popover-input>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { verifyPassWord, pageList, restore, backupObj } from '/@/api/basicData/backups';

import { useI18n } from 'vue-i18n';
const PopoverInput = defineAsyncComponent(() => import('/@/components/PopoverInput/index.vue'));

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();
const inputPassword = ref();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	isPage: false,
	pageList: pageList,
});
const { getDataList, tableStyle } = useTable(state);

const backupsClick = async () => {
	try {
		await backupObj();
		useMessage().success('备份成功');
		getDataList();
	} catch (error: any) {
		useMessage().error(error.msg);
	}
};
let loading = ref(false);
//修改用户密码
const changePassword = async (row: any) => {
	try {
		await verifyPassWord(inputPassword.value);
	} catch (error: any) {
		useMessage().error(error.msg);
		return;
	}
	try {
		loading.value = true;

		await restore(row);
		useMessage().success('还原成功');
	} catch (error: any) {
		useMessage().error(error.msg);
	} finally {
		loading.value = false;
	}
};
</script>
