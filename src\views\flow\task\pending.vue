<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row shadow="hover" v-show="showSearch" class="ml10">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
					<el-form-item label="流程" prop="processName">
						<el-input placeholder="请输入流程名称" v-model="state.queryForm.processName" />
					</el-form-item>
					<el-form-item label="发起时间" prop="taskTime">
						<el-date-picker type="datetimerange" value-format="YYYY-MM-DD HH:mm:ss" v-model="state.queryForm.taskTime" is-range range-separator="-" />
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList">
							{{ $t('common.queryBtn') }}
						</el-button>
						<el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
					</el-form-item>
				</el-form>
			</el-row>

			<el-row>
				<div class="mb8" style="width: 100%">
					<right-toolbar
						v-model:showSearch="showSearch"
						class="ml10"
						style="float: right; margin-right: 20px"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>

			<el-table
				ref="dataTableRef"
				v-loading="loading"
				:data="state.dataList"
				highlight-current-row
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="分组" prop="groupName" width="100" />
				<el-table-column label="流程" prop="processName" />
				<el-table-column label="发起人" prop="rootUserName" />
				<el-table-column label="发起时间" prop="startTime" />
				<el-table-column label="当前节点" prop="taskName" />
				<el-table-column label="任务时间" prop="taskCreateTime" />
				<el-table-column fixed="right" label="操作" width="200">
					<template #default="scope">
						<el-button type="primary" size="small" link icon="VideoPlay" @click="deal(scope.row)"> 开始处理 </el-button>
					</template>
				</el-table-column>
			</el-table>

			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination"></pagination>

			<!--			右侧抽屉-->
			<el-drawer v-model="rightDrawerVisible" v-if="rightDrawerVisible" direction="rtl" size="600px">
				<template #header>
					<h3>{{ currentData?.processName }}</h3>
				</template>
				<template #default>
					<el-card class="box-card">
						<FormCreate :rule="rule" v-model="formData" v-model:api="fApi" />
					</el-card>
					<flow-node-format
						:disableSelect="true"
						:task-id="currentData.taskId"
						:processInstanceId="currentData.processInstanceId"
						:flow-id="currentData.flowId"
						ref="flowNodeFormatRef"
						class="mt-4"
					/>
				</template>
				<template #footer>
					<div style="flex: auto">
						<el-button size="large" type="danger" icon="Close" @click="refuseTask">拒绝</el-button>
						<el-button size="large" type="primary" icon="Check" @click="submitTask">提交</el-button>
						<el-button size="large" type="info" icon="Share" @click="transferTask">转办</el-button>
					</div>
				</template>
			</el-drawer>

			<!--同意提交处理-->
			<agree-handle @taskSubmitEvent="taskSubmitEvent" ref="agreeHandler"></agree-handle>

			<!--拒绝审核处理-->
			<refuse-handle @taskSubmitEvent="taskSubmitEvent" ref="refuseHandler"></refuse-handle>

			<!--转办处理-->
			<transfer-handle @taskSubmitEvent="taskSubmitEvent" ref="transferHandler"></transfer-handle>
		</div>
	</div>
</template>
<script setup lang="ts">
import AgreeHandle from './handler/agree.vue';
import RefuseHandle from './handler/refuse.vue';
import TransferHandle from './handler/transfer.vue';
import FlowNodeFormat from '/@/views/flow/form/tools/FlowNodeFormatData.vue';
import { queryMineTask, queryTask } from '/@/api/flow/task';
import { BasicTableProps, useTable } from '/@/hooks/table';
import FcDesigner from 'form-create-designer';
import { processFormItemsWithPerms } from '/@/views/flow/workflow/utils/formPermissions';
import FormCreate from '/@/views/flow/workflow/components/FormCreate.vue';

const rightDrawerVisible = ref(false);
const showSearch = ref(true);
const loading = ref(false);
const queryRef = ref();

const fApi = ref();
const formData = ref({});

// Define the FormItem interface if not already defined
interface FormItem {
	// Define the properties of FormItem based on your data structure
}

const rule = ref<FormItem[]>([]);

const currentData = ref();
const state: BasicTableProps = reactive<BasicTableProps>({
	pageList: queryMineTask,
	queryForm: {
		processName: '',
		taskTime: undefined,
	},
});

const { tableStyle, getDataList, currentChangeHandle, sizeChangeHandle } = useTable(state);

/**
 * 点击开始处理
 * @param row
 */
const deal = (row: any) => {
	currentData.value = row;
	queryTask(row.taskId, false).then((res) => {
		const { formItems, formPerms, formData: responseFormData } = res.data;

		// 解析表单项
		const parsedFormItems = FcDesigner.formCreate.parseJson(formItems);

		// 递归处理所有表单项的权限
		const itemsWithPerms = processFormItemsWithPerms(parsedFormItems, formPerms);

		rule.value = itemsWithPerms;
		formData.value = responseFormData;
		currentOpenFlowForm.value = formItems;
		rightDrawerVisible.value = true;
	});
};
const currentOpenFlowForm = ref();

const agreeHandler = ref();
const refuseHandler = ref();
const transferHandler = ref();

// 清空搜索条件
const resetQuery = () => {
	queryRef.value.resetFields();
	getDataList();
};

const taskSubmitEvent = () => {
	rightDrawerVisible.value = false;
	getDataList();
};

/**
 * 提交任务
 */
const submitTask = () => {
	agreeHandler.value.handle(currentData.value, currentOpenFlowForm.value);
};
/**
 * 拒绝任务
 */
const refuseTask = () => {
	refuseHandler.value.handle(currentData.value, currentOpenFlowForm.value);
};

/**
 * 转办任务
 */
const transferTask = () => {
	transferHandler.value.handle(currentData.value, currentOpenFlowForm.value);
};

onMounted(() => {
	getDataList();
});
</script>
