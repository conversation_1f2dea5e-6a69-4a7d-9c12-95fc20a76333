import request from '/@/utils/request';

export const getConfigurationObj = () => {
	return request({
		url: '/admin/printerConfig/getPrinterConfigWrapper',
		method: 'get',
	});
};
//工作站下拉框
export const getWorkstation = () => {
	return request({
		url: '/admin/rfidDevice/getRfidDeviceListTwo',
		method: 'get',
	});
};
//获取打印机
export const getPrinter = () => {
	return request({
		url: '/admin/rfidDevice/getRfidDeviceListFour',
		method: 'get',
	});
};

export const putObj = (data?: Object) => {
	return request({
		url: '/admin/printerConfig/updateDictionaryWarnings',
		method: 'post',
		data,
	});
};
