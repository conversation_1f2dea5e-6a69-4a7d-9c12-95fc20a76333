<template>
	<el-dialog :title="false ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable width="600">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="100px" >
			<el-form-item label="仓库" prop="warehouseId">
				<el-select v-model="form.warehouseId" placeholder="请选择仓库" @change="getZoneCodeSelect">
					<el-option v-for="item in warehouseData" :key="item.id" :label="item.warehouseName" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="区号" prop="warehouseZoneId">
				<el-select v-model="form.warehouseZoneId" placeholder="请选择区号" @change="getColumnSelect">
					<el-option v-for="item in zoneCodeData" :key="item.id" :label="item.zoneCode" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="列号" prop="shelfCode">
				<el-select v-model="form.shelfCode" placeholder="请选择列号" @change="columnChange">
					<el-option v-for="item in columnCodeData" :key="item.id" :label="item.shelfCode" :value="item.shelfCode" />
				</el-select>
			</el-form-item>
			<el-form-item label="面" prop="faceCode" v-if="faceStatus == '1'">
				<el-select v-model="form.faceCode" placeholder="请选择面" @change="faceChange">
					<el-option
						v-for="item in [
							{ label: '左面', value: '01' },
							{ label: '右面', value: '02' },
						]"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="指示灯类型" prop="indicatorType">
				<el-select v-model="form.indicatorType" placeholder="请选择指示灯类型">
					<el-option
						v-for="item in [
							{ label: '电子标签', value: 1 },
							{ label: '巷道灯', value: 2 },
						]"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
			</el-form-item>

			<el-form-item label="巷道位置" prop="locationCode">
				<el-input v-model="form.locationCode" placeholder="自动生成" disabled />
			</el-form-item>
			<el-form-item label="指示灯mac" prop="mac">
				<el-input v-model="form.mac" placeholder="请输入指示灯mac" />

			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="SysMessageHookDialog">
import { useMessage } from '/@/hooks/message';
import { addObj, getWarehouse, getArea, getColumn } from '/@/api/basicData/locationManagement/lamp';
import { useI18n } from 'vue-i18n';

const emit = defineEmits(['refresh']);

 const { t } = useI18n();
// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 提交表单数据
const form = reactive<any>({
	warehouseId: '',
	warehouseZoneId: '',
	shelfCode: '',
	faceCode: '',
	locationCode: '',
	indicatorType: '',
	mac: '',
});

// 定义校验规则
const dataRules = ref({
	warehouseId: [{ required: true, message: '仓库不能为空', trigger: 'change' }],
	warehouseZoneId: [{ required: true, message: '区号不能为空', trigger: 'change' }],
	indicatorType: [{ required: true, message: '指示灯类型不能为空', trigger: 'change' }],
	mac: [{ required: true, message: '指示灯mac不能为空', trigger: 'change' }],
});

// 打开弹窗
const openDialog = (id: string) => {
	positionEncoding.value = new Array(6);
	visible.value = true;
	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});
	getWarehouseSelect();
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;

		await addObj(form);
		useMessage().success(t('common.addSuccessText'));
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

let faceStatus = ref('0');
let warehouseData = ref<any>([]);
let zoneCodeData = ref<any>([]);
let columnCodeData = ref<any>([]);

let positionEncoding = ref(new Array(4));
//仓库下拉
const getWarehouseSelect = async () => {
	warehouseData.value = (await getWarehouse())?.data || [];
};
// 更新位置编码和位置条码的通用函数
const updatePositionAndBarcode = (index: number, prefix: string, value: string) => {
	positionEncoding.value[index] = prefix + value;

};

// 仓库获取区号下拉
const getZoneCodeSelect = async (id: string) => {
	resetForm(['warehouseZoneId', 'shelfCode', 'faceCode']);
	zoneCodeData.value = (await getArea(id))?.data || [];
	const warehouseCode = warehouseData.value.find((item: any) => item.id === id)?.warehouseCode;
	updatePositionAndBarcode(0, 'W', warehouseCode || '');
	positionEncoding.value[1] = ''
	positionEncoding.value[2] = ''
	positionEncoding.value[3] = ''

	generate();
};

// 区获取列号下拉
const getColumnSelect = async (id: string) => {
	resetForm(['shelfCode', 'faceCode']);
	columnCodeData.value = (await getColumn(id))?.data || [];
	const zoneCode = zoneCodeData.value.find((item: any) => item.id === id)?.zoneCode;
	faceStatus.value = zoneCodeData.value.find((item: any) => item.id === id)?.faceStatus;
	updatePositionAndBarcode(1, 'Z', zoneCode || '');
	positionEncoding.value[2] = ''
	positionEncoding.value[3] = ''
	generate();
};

// 列号触发
const columnChange = (value: string) => {
	resetForm(['faceCode']);
	updatePositionAndBarcode(2, 'S', value);
	generate();
};

// 面获取 组下拉
const faceChange = async (value: string) => {
	updatePositionAndBarcode(3, 'F', value);
	generate();
};

// 计算位置编码 位置条码
const generate = () => {
	form.locationCode = positionEncoding.value.join('');
};
//重置表单方法
const resetForm = (arr: any[]) => {
	arr.forEach((item) => {
		form[item] = '';
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
