<template>
	<el-dialog :title="false ? $t('common.editBtn') : $t('common.addBtn')" v-model="visible" width="600" :close-on-click-modal="false" draggable>
		<el-form ref="menuDialogFormRef" :model="state.ruleForm" :rules="dataRules" label-width="120px">
			<el-form-item label="盘点任务名称" prop="taskName">
				<el-input v-model="state.ruleForm.taskName" placeholder="请输入盘点任务名称" />
			</el-form-item>
			<el-form-item label="盘点类型" prop="checkType">
				<el-radio-group v-model="state.ruleForm.checkType">
					<el-radio border label="0">按仓库位置</el-radio>
					<el-radio border label="1">按物资目录</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="所属仓库" prop="warehouseId">
				<el-select placeholder="请选择所属仓库" v-model="state.ruleForm.warehouseId" clearable @change="getZoneCodeSelect">
					<el-option :key="index" :label="item.warehouseName" :value="item.id" v-for="(item, index) in warehouseData"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="区号" prop="zoneCode" v-if="state.ruleForm.checkType == '0'">
				<el-select placeholder="请选择区号" v-model="state.ruleForm.warehouseZoneId" clearable @change="getColumnSelect">
					<el-option :key="index" :label="item.zoneCode" :value="item.id" v-for="(item, index) in zoneCodeData"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="列号" prop="shelfCode" v-if="state.ruleForm.checkType == '0'">
				<el-select placeholder="请选择列号 " v-model="state.ruleForm.shelfCode" clearable>
					<el-option :key="index" :label="item.shelfCode" :value="item.shelfCode" v-for="(item, index) in columnCodeData"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="物资名称" v-if="state.ruleForm.checkType == '1'">
				<el-select
					v-model="state.ruleForm.materialCatalogIdList"
					filterable
					remote
					clearable
					reserve-keyword
					multiple
					placeholder="请选择物资名称"
					:remote-method="remoteMethod"
				>
					<el-option v-for="item in options" :key="item.id" :label="item.materialName" :value="item.id" />
				</el-select>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="systemMenuDialog">
import { useI18n } from 'vue-i18n';
import { useMessage } from '/@/hooks/message';
import { getMaterialName } from '/@/api/storage/statistics';
import { getArea, getColumn, getWarehouse } from '/@/api/basicData/locationManagement/label';
import { addObj } from '/@/api/storage/stocktaking';

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
const { t } = useI18n();
// 引入组件

// 定义变量内容
const visible = ref(false);
const loading = ref(false);
const menuDialogFormRef = ref();
// 定义需要的数据
const state = reactive({
	ruleForm: {
		taskName: '',
		checkType: '0',
		warehouseId: '',
		warehouseZoneId: '',
		shelfCode: '',
		warehouseLocationId: '',
		materialCatalogIdList: '',
	},
	parentData: [] as any[], // 上级菜单数据
});

// 表单校验规则
const dataRules = reactive({
	taskName: [{ required: true, message: '盘点任务名称不能为空', trigger: 'blur' }],
	checkType: [{ required: true, message: '盘点类型不能为空', trigger: 'blur' }],
	warehouseId: [{ required: true, message: '仓库不能为空', trigger: 'blur' }],
	materialCatalogIdList: [{ required: true, message: '物资名称不能为空', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = (type: string, row?: any) => {
	visible.value = true;

	nextTick(() => {
		menuDialogFormRef.value?.resetFields();
	});
};

// 保存数据
const onSubmit = async () => {
	const valid = await menuDialogFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		let params = {
			...state.ruleForm,
		};
		if (params.warehouseZoneId) {
			params.warehouseLocationId = params.warehouseZoneId;
		}
		if (params.shelfCode) {
			params.warehouseLocationId = params.shelfCode;
		}
		loading.value = true;
		await addObj(params);
		useMessage().success(t('common.addSuccessText'));
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

let options = ref<any>([]);

const remoteMethod = (query: string) => {
	if (query) {
		setTimeout(() => {
			getMaterialName({ materialName: query }).then((res: any) => {
				const list = res.data;
				options.value = list.filter((item: any) => {
					return item.materialName.toLowerCase().includes(query.toLowerCase());
				});
			});
		}, 1000);
	} else {
		options.value = [];
	}
};

let warehouseData = ref<any[]>([]);
let zoneCodeData = ref<any[]>([]);
let columnCodeData = ref<any[]>([]);
const getWarehouseSelect = async () => {
	warehouseData.value = (await getWarehouse())?.data || [];
};
const getZoneCodeSelect = async (id: string) => {
	state.ruleForm.warehouseZoneId = '';
	state.ruleForm.shelfCode = '';
	if (!id) return;
	zoneCodeData.value = (await getArea(id))?.data || [];
};
const getColumnSelect = async (id: string) => {
	state.ruleForm.shelfCode = '';
	if (!id) return;
	columnCodeData.value = (await getColumn(id))?.data || [];
};
// 暴露变量 只有暴漏出来的变量 父组件才能使用
defineExpose({
	openDialog,
});
onMounted(async () => {
	await getWarehouseSelect();
});
</script>
