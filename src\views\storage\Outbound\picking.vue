<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full flex justify-between items-center">
					<div class="flex items-center cursor-pointer" @click="returnClick">
						<img src="/@/assets/back.png" class="w-5 h-5 mr-1" />
						<div class="text-14" style="color: #2e5cf6">返回</div>
					</div>
					<div>
						<el-button @click="locateClick">定位</el-button>
						<el-button @click="lightingClick">复位</el-button>
						<el-button @click="exportPdf">导出</el-button>
					</div>
				</div>
			</el-row>
			<el-row class="mt20">
				<el-col :span="24">
					<Descriptions title="拣货单" :column="5" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>

						<DescriptionsItem label="出库单编号">{{ detailsData?.billCode }}</DescriptionsItem>
						<DescriptionsItem label="出库仓库">{{ detailsData?.warehouseName }}</DescriptionsItem>
						<DescriptionsItem label="出库单状态">{{
							detailsData?.billStatus == 0 ? '待确认' : detailsData?.billStatus == 1 ? '待出库' : detailsData?.billStatus == 2 ? '已出库' : ''
						}}</DescriptionsItem>

						<DescriptionsItem label="出库用途">{{ detailsData?.outPurpose }}</DescriptionsItem>
						<DescriptionsItem label="是否归还">{{ detailsData?.needReturn == 1 ? '是' : detailsData?.needReturn == 0 ? '否' : '' }}</DescriptionsItem>
						<DescriptionsItem label="预计归还时间" v-if="detailsData?.needReturn == 1">{{ detailsData?.planReturnTime }}</DescriptionsItem>
						<DescriptionsItem label="申请部门">{{ detailsData?.applyDept }}</DescriptionsItem>
						<DescriptionsItem label="申请人员">{{ detailsData?.applyUser }}</DescriptionsItem>
						<DescriptionsItem label="创建人">{{ detailsData?.createUser }}</DescriptionsItem>
						<DescriptionsItem label="创建时间">{{ detailsData?.createTime }}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>
			<el-row class="mt10 mb10">出库物资清单</el-row>
			<el-table
				v-loading="state.loading"
				:data="tableData"
				row-key="userId"
				max-height="calc(100vh - 400px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
				<el-table-column label="推荐位置及数量" width="300">
					<template #header>
						<div>
							<span>推荐位置及数量</span>
							<el-tooltip class="box-item" effect="light" content="全部收起" placement="top-start">
								<el-button v-if="tableData.some((row:any) => needsExpansion(row.barCode))" link type="primary" size="small" @click="toggleAllExpand">
									<img :src="zdImg" v-show="isAllExpanded" />
								</el-button>
							</el-tooltip>
						</div>
					</template>
					<template #default="scope">
						<div v-if="scope.row.barCode" class="ml-[10px]">
							<div class="flex justify-items-start">
								<div class="mr10">
									<div v-for="(code, index) in getDisplayCodes(scope.row)" :key="index">
										<span>{{ code.split('#')[0] }}：{{ code.split('#')[1] }}</span>
									</div>
								</div>

								<el-button v-if="needsExpansion(scope.row.barCode)" link type="primary" size="small" @click="toggleExpand(scope.row, scope.$index)">
									<img :src="scope.row.isExpanded ? zdImg : zkImg" :alt="scope.row.isExpanded ? '收起' : '展开'" />
								</el-button>
							</div>
						</div>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { getPickingObj, positioning, lighting } from '/@/api/storage/outbound';

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 动态引入组件

const { t } = useI18n();

const state: BasicTableProps = reactive<BasicTableProps>({});
const { downBlobFile, tableStyle } = useTable(state);

const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/Outbound/index' });
};
//表格数据
const tableData = ref<any>([]);
const detailsData = ref<any>();
const route = useRoute();
const getData = async () => {
	let { data } = await getPickingObj(route.query?.id);
	detailsData.value = data;
	tableData.value = data.pickingDetailVOList.map((item: any) => {
		return {
			...item,
			barCode: item.referralsDetailVOList.map((item: any) => item.locationCode).join(','),
			barCodeNum: item.referralsDetailVOList.map((item: any) => item.num).join(','),
			isExpanded: false,
		};
	});
};
onMounted(() => {
	getData();
});
// 表格内展开折叠
import zkImg from '/@/assets/flod.png';
import zdImg from '/@/assets/open.png';
const MAX_DISPLAY_LINES = 2;
//table ref
const tableRefs = ref();
//全部数据，截取最大行数   截取字段为row.barCode
const getDisplayCodes = (row: any) => {
	const barCode = row.barCode;
	const barCodeNum = row.barCodeNum;
	if (!barCode) return [];
	const codeNum = barCodeNum.split(',');
	const codes = barCode.split(',').map((item: any, index: number) => {
		return item + '#' + codeNum[index];
	});
	if (codes.length > MAX_DISPLAY_LINES && !row.isExpanded) {
		return [...codes.slice(0, MAX_DISPLAY_LINES - 1)];
	}

	return codes;
};
//table滚动到指定行
const scrollToRow = (tableRef: any, rowIndex: number) => {
	nextTick(() => {
		if (tableRef.value) {
			// @ts-ignore
			const tableBody = tableRef.value.$el.querySelector('.el-table__body-wrapper');
			if (tableBody) {
				const row = tableBody.querySelectorAll('.el-table__row')[rowIndex - 1];
				if (row) {
					row.scrollIntoView({ behavior: 'smooth' });
				}
			}
		}
	});
};
//将所有行 收起
const toggleAllExpand = () => {
	tableData.value.forEach((row: any) => {
		row.isExpanded = false;
	});
};
//有展开的行  则展示表头收起图标
const isAllExpanded = computed(() => tableData.value.some((row: any) => row.isExpanded));
//判断长度需要展开字段的数据长度
const needsExpansion = (barCode?: string) => {
	return barCode ? barCode.split(',').length > MAX_DISPLAY_LINES : false;
};
//行内展开收起事件
const toggleExpand = (row: any, rowIndex: any) => {
	row.isExpanded = !row.isExpanded;
	if (!row.isExpanded) scrollToRow(tableRefs, rowIndex + 1);
};

//locateClick
const locateClick = async () => {
	let b = tableData.value
		.map((item: any) => {
			return item.referralsDetailVOList.map((detail: any) => {
				return {
					materialCode: item.materialCode,
					num: detail.num,
					locationCode: detail.locationCode,
				};
			});
		})
		.flat();

	let params = {
		warehouseId: detailsData.value.warehouseId,
		billCode: detailsData.value.billCode,
		billDetailList: b,
	};
	try {
		let res = await positioning(params);
		useMessage().success('定位成功');
	} catch (error: any) {
		useMessage().error(error.msg);
	}
};

//灭灯
const lightingClick = async () => {
	let params = {
		warehouseId: detailsData.value.warehouseId,
		billCode: detailsData.value.billCode,
	};
	try {
		let res = await lighting(params);
		useMessage().success('灭灯成功');
	} catch (error: any) {
		useMessage().error(error.msg);
	}
};

const exportPdf = async () => {
	downBlobFile(`/admin/outWarehouseBill/pdfOutWarehouseBillPickingById/${route.query?.id}`, '', '拣货单.pdf');
};
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
