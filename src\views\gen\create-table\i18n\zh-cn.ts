export default {
	createTable: {
		index: '#',
		importcreateTableTip: '导入自动创建表管理',
		id: '主键ID',
		tableName: '表名称',
		comments: '表注释',
		comment: '字段注释',
		databaseType: '数据库类型',
		pkPolicy: '主键策略',
		createUser: '创建人',
		createTime: '创建时间',
		columnInfo: '字段信息',
		tenantId: '租户ID',
		inputIdTip: '请输入主键ID',
		inputTableNameTip: '请输入表名称',
		inputCommentsTip: '请输入表注释',
		inputCommentTip: '请输入字段注释',
		inputDatabaseTypeTip: '请输入数据库引擎',
		inputPkPolicyTip: '请输入主键策略',
		inputCreateUserTip: '请输入创建人',
		inputCreateTimeTip: '请输入创建时间',
		inputColumnInfoTip: '请输入字段信息',
		inputTenantIdTip: '请输入租户ID',
		name: '字段名称',
		typeName: '字段类型',
		precision: '字段长度',
		scale: '小数位数',
		defaultValue: '默认值',
		primary: '主键',
		nullable: 'NULL',
	},
};
