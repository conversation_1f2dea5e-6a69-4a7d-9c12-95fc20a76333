<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full flex justify-between items-center">
					<div class="flex items-center cursor-pointer" @click="returnClick">
						<img src="/@/assets/back.png" class="w-5 h-5 mr-1" />
						<div class="text-14" style="color: #2e5cf6">返回</div>
					</div>
					<div>
						<el-button @click="urgeReturnClick">催还</el-button>
						<el-button @click="returnClick">导出</el-button>
					</div>
				</div>
			</el-row>
			<el-row class="mt20">
				<el-col :span="24">
					<Descriptions :column="4" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>
						<DescriptionsItem label="出库单编号">{{ pendingInquiryVO?.billCode }}</DescriptionsItem>
						<DescriptionsItem label="出库单状态">{{
							pendingInquiryVO?.billStatus == 1
								? '待出库'
								: pendingInquiryVO?.billStatus == 0
								? '待确认'
								: pendingInquiryVO?.billStatus == 2
								? '已出库'
								: ''
						}}</DescriptionsItem>
						<DescriptionsItem label="出库仓库">{{ pendingInquiryVO?.warehouseName }}</DescriptionsItem>
						<DescriptionsItem label="出库用途">{{ pendingInquiryVO?.outPurpose }}</DescriptionsItem>
						<DescriptionsItem label="是否归还">{{
							pendingInquiryVO?.needReturn == 1 ? '是' : pendingInquiryVO?.needReturn == 0 ? '否' : ''
						}}</DescriptionsItem>
						<DescriptionsItem label="预计归还时间">{{ pendingInquiryVO?.outTime }}</DescriptionsItem>
						<DescriptionsItem label="申请部门">{{ pendingInquiryVO?.nameDept }}</DescriptionsItem>
						<DescriptionsItem label="申请人员">{{ pendingInquiryVO?.applyUser }}</DescriptionsItem>
						<DescriptionsItem label="创建人">{{ pendingInquiryVO?.createUser }}</DescriptionsItem>
						<DescriptionsItem label="创建时间">{{ pendingInquiryVO?.createTime }}</DescriptionsItem>
						<DescriptionsItem label="出库人">{{ pendingInquiryVO?.outUser }}</DescriptionsItem>
						<DescriptionsItem label="出库时间">{{ pendingInquiryVO?.outTime }}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>

			<el-row>
				<div class="mb-2 w-full"></div>
			</el-row>
			<el-table
				:data="materialCatalogList"
				row-key="userId"
				max-height="calc(100vh - 400px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="未归还数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" width="200" fixed="right">
					<template #default="scope">
						<!-- 修改信息 -->
						<el-button  text type="primary" @click="LogDetailRef.openDialog(scope.row.materialCode)"> 明细 </el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<log-detail ref="LogDetailRef"></log-detail>
	</div>
</template>

<script lang="ts" setup>
import { getObj, urgeReturn } from '/@/api/storage/waitingReturn';
import { useMessage } from '/@/hooks/message';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

const LogDetail = defineAsyncComponent(() => import('./detail.vue'));
const route = useRoute();
const router = useRouter();
const LogDetailRef = ref();
const { t } = useI18n();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);

let materialCatalogList = ref([]);
let pendingInquiryVO = ref<any>({});

const getData = () => {
	getObj(route.query?.id).then((res: any) => {
		let { materialCatalogList} = res.data;
		materialCatalogList.value = materialCatalogList;
		pendingInquiryVO.value = res.data;
	});
};
//催还
const urgeReturnClick = async () => {
	try {
		await urgeReturn(pendingInquiryVO.value?.billCode);
		useMessage().success('催还成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
	}
};

const returnClick = () => {
	router.replace({ path: '/storage/waitingReturn/index' });
};

onMounted(() => {
	getData();
});
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
